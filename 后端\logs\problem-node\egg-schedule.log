2025-07-30 11:51:04,427 INFO 2348 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 11:51:04,427 INFO 2348 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 11:51:04,427 INFO 2348 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 11:51:04,427 INFO 2348 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 11:51:05,117 INFO 31080 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js next time will execute after 59934885ms at 2025-07-31 04:30:00.002
2025-07-30 11:51:05,118 INFO 31080 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js next time will execute after 43734882ms at 2025-07-31 00:00:00.000
2025-07-30 11:51:05,118 INFO 31080 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js next time will execute after 43735882ms at 2025-07-31 00:00:01.000
2025-07-30 11:51:05,119 INFO 31080 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 77634882ms at 2025-07-31 09:25:00.001
2025-07-30 15:22:05,645 INFO 34744 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js next time will execute after 47274358ms at 2025-07-31 04:30:00.003
2025-07-30 15:22:05,645 INFO 34744 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js next time will execute after 31074355ms at 2025-07-31 00:00:00.000
2025-07-30 15:22:05,646 INFO 34744 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js next time will execute after 31075355ms at 2025-07-31 00:00:01.001
2025-07-30 15:22:05,646 INFO 34744 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 64974354ms at 2025-07-31 09:25:00.000
2025-07-30 15:22:04,884 INFO 27972 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 15:22:04,885 INFO 27972 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 15:22:04,885 INFO 27972 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 15:22:04,885 INFO 27972 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 15:24:34,476 INFO 33404 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js next time will execute after 47125528ms at 2025-07-31 04:30:00.004
2025-07-30 15:24:34,476 INFO 33404 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js next time will execute after 30925524ms at 2025-07-31 00:00:00.000
2025-07-30 15:24:34,477 INFO 33404 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js next time will execute after 30926524ms at 2025-07-31 00:00:01.001
2025-07-30 15:24:34,478 INFO 33404 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 64825523ms at 2025-07-31 09:25:00.001
2025-07-30 15:24:33,700 INFO 27136 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 15:24:33,700 INFO 27136 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 15:24:33,700 INFO 27136 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 15:24:33,700 INFO 27136 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 15:27:47,478 INFO 17724 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 15:27:47,478 INFO 17724 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 15:27:47,478 INFO 17724 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 15:27:47,478 INFO 17724 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 15:27:55,470 INFO 21228 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 15:27:55,470 INFO 21228 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 15:27:55,470 INFO 21228 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 15:27:55,470 INFO 21228 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 15:29:36,102 INFO 30196 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 15:29:36,102 INFO 30196 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 15:29:36,102 INFO 30196 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 15:29:36,102 INFO 30196 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 15:31:29,784 INFO 19412 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 15:31:29,785 INFO 19412 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 15:31:29,785 INFO 19412 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 15:31:29,785 INFO 19412 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:16:19,461 INFO 2276 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:16:19,461 INFO 2276 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:16:19,461 INFO 2276 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:16:19,461 INFO 2276 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:18:48,289 INFO 14128 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:18:48,290 INFO 14128 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:18:48,290 INFO 14128 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:18:48,290 INFO 14128 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:18:54,369 INFO 35460 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:18:54,369 INFO 35460 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:18:54,369 INFO 35460 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:18:54,369 INFO 35460 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:21:16,657 INFO 38496 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js next time will execute after 43723345ms at 2025-07-31 04:30:00.002
2025-07-30 16:21:16,658 INFO 38496 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js next time will execute after 27523342ms at 2025-07-31 00:00:00.000
2025-07-30 16:21:16,658 INFO 38496 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js next time will execute after 27524342ms at 2025-07-31 00:00:01.000
2025-07-30 16:21:16,659 INFO 38496 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 61423342ms at 2025-07-31 09:25:00.001
2025-07-30 16:21:15,951 INFO 3504 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:21:15,951 INFO 3504 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:21:15,951 INFO 3504 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:21:15,951 INFO 3504 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:25:06,458 INFO 28620 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:25:06,459 INFO 28620 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:25:06,459 INFO 28620 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:25:06,459 INFO 28620 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:25:25,758 INFO 29896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:25:25,758 INFO 29896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:25:25,758 INFO 29896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:25:25,758 INFO 29896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:25:38,805 INFO 15172 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:25:38,805 INFO 15172 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:25:38,805 INFO 15172 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:25:38,805 INFO 15172 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:25:53,746 INFO 21672 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:25:53,746 INFO 21672 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:25:53,746 INFO 21672 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:25:53,746 INFO 21672 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:26:09,464 INFO 28544 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:26:09,464 INFO 28544 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:26:09,464 INFO 28544 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:26:09,464 INFO 28544 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:26:18,664 INFO 19332 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:26:18,665 INFO 19332 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:26:18,665 INFO 19332 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:26:18,665 INFO 19332 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:26:42,870 INFO 35504 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:26:42,870 INFO 35504 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:26:42,871 INFO 35504 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:26:42,871 INFO 35504 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:26:52,905 INFO 35784 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:26:52,905 INFO 35784 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:26:52,905 INFO 35784 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:26:52,905 INFO 35784 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:27:08,483 INFO 12452 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:27:08,483 INFO 12452 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:27:08,483 INFO 12452 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:27:08,483 INFO 12452 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:27:17,390 INFO 36896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:27:17,390 INFO 36896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:27:17,390 INFO 36896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:27:17,390 INFO 36896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:27:49,338 INFO 38684 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:27:49,338 INFO 38684 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:27:49,338 INFO 38684 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:27:49,338 INFO 38684 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:27:57,243 INFO 30144 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:27:57,244 INFO 30144 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:27:57,244 INFO 30144 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:27:57,244 INFO 30144 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:28:16,587 INFO 20320 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:28:16,588 INFO 20320 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:28:16,588 INFO 20320 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:28:16,588 INFO 20320 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:28:26,171 INFO 21076 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:28:26,171 INFO 21076 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:28:26,171 INFO 21076 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:28:26,172 INFO 21076 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:28:31,598 INFO 4208 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:28:31,599 INFO 4208 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:28:31,599 INFO 4208 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:28:31,599 INFO 4208 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:39:59,558 INFO 27532 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:39:59,558 INFO 27532 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:39:59,558 INFO 27532 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:39:59,558 INFO 27532 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 16:49:41,614 INFO 35672 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 16:49:41,614 INFO 35672 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 16:49:41,614 INFO 35672 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 16:49:41,614 INFO 35672 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-30 17:26:24,009 INFO 29020 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-30 17:26:24,009 INFO 29020 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-30 17:26:24,009 INFO 29020 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-30 17:26:24,009 INFO 29020 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 09:23:59,976 INFO 25392 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js next time will execute after 68760027ms at 2025-08-01 04:30:00.003
2025-07-31 09:23:59,977 INFO 25392 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js next time will execute after 52560023ms at 2025-08-01 00:00:00.000
2025-07-31 09:23:59,977 INFO 25392 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js next time will execute after 52561023ms at 2025-08-01 00:00:01.000
2025-07-31 09:23:59,978 INFO 25392 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 60023ms at 2025-07-31 09:25:00.001
2025-07-31 09:23:59,187 INFO 4688 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 09:23:59,187 INFO 4688 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 09:23:59,187 INFO 4688 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 09:23:59,187 INFO 4688 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 09:24:02,763 INFO 28900 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 09:24:02,763 INFO 28900 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 09:24:02,763 INFO 28900 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 09:24:02,763 INFO 28900 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 09:25:00,021 INFO 28900 [Job#175392510001530171483318001] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js executing by app
2025-07-31 09:25:00,024 ERROR 28900 [Job#175392510001530171483318001] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js execute failed, used 1ms. TypeError: Cannot read properties of undefined (reading 'get_report_result')
    at Object.task (C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js:10:29)
    at C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-schedule\app.js:52:31
    at AsyncLocalStorage.run (node:async_hooks:346:14)
    at Messenger.<anonymous> (C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-schedule\app.js:51:28)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 09:25:00,032 INFO 25392 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 86399980ms at 2025-08-01 09:25:00.012
2025-07-31 09:46:34,194 INFO 30892 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 09:46:34,195 INFO 30892 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 09:46:34,195 INFO 30892 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 09:46:34,195 INFO 30892 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 09:46:34,888 INFO 22320 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js next time will execute after 67405114ms at 2025-08-01 04:30:00.002
2025-07-31 09:46:34,889 INFO 22320 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js next time will execute after 51205111ms at 2025-08-01 00:00:00.000
2025-07-31 09:46:34,889 INFO 22320 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js next time will execute after 51206111ms at 2025-08-01 00:00:01.000
2025-07-31 09:46:34,890 INFO 22320 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 85105111ms at 2025-08-01 09:25:00.001
2025-07-31 10:26:51,823 INFO 11772 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 10:26:51,823 INFO 11772 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 10:26:51,823 INFO 11772 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 10:26:51,823 INFO 11772 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 10:26:52,627 INFO 3300 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js next time will execute after 64987376ms at 2025-08-01 04:30:00.003
2025-07-31 10:26:52,627 INFO 3300 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js next time will execute after 48787373ms at 2025-08-01 00:00:00.000
2025-07-31 10:26:52,628 INFO 3300 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js next time will execute after 48788373ms at 2025-08-01 00:00:01.001
2025-07-31 10:26:52,629 INFO 3300 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 82687372ms at 2025-08-01 09:25:00.001
2025-07-31 10:41:27,998 INFO 9720 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 10:41:27,998 INFO 9720 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 10:41:27,998 INFO 9720 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 10:41:27,998 INFO 9720 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 10:41:28,672 INFO 32264 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js next time will execute after 64111331ms at 2025-08-01 04:30:00.003
2025-07-31 10:41:28,673 INFO 32264 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js next time will execute after 47911328ms at 2025-08-01 00:00:00.000
2025-07-31 10:41:28,673 INFO 32264 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js next time will execute after 47912327ms at 2025-08-01 00:00:01.000
2025-07-31 10:41:28,673 INFO 32264 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 81811327ms at 2025-08-01 09:25:00.000
2025-07-31 11:37:39,104 INFO 22108 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 11:37:39,104 INFO 22108 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 11:37:39,104 INFO 22108 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 11:37:39,104 INFO 22108 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 11:41:51,853 INFO 33752 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 11:41:51,854 INFO 33752 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 11:41:51,854 INFO 33752 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 11:41:51,854 INFO 33752 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 11:42:06,566 INFO 6180 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 11:42:06,566 INFO 6180 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 11:42:06,567 INFO 6180 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 11:42:06,567 INFO 6180 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 11:42:14,507 INFO 27788 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 11:42:14,508 INFO 27788 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 11:42:14,508 INFO 27788 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 11:42:14,508 INFO 27788 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 11:43:31,396 INFO 7412 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 11:43:31,396 INFO 7412 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 11:43:31,396 INFO 7412 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 11:43:31,396 INFO 7412 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 11:43:38,899 INFO 24572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 11:43:38,899 INFO 24572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 11:43:38,899 INFO 24572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 11:43:38,899 INFO 24572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 11:43:47,195 INFO 10536 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 11:43:47,195 INFO 10536 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 11:43:47,195 INFO 10536 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 11:43:47,195 INFO 10536 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 15:30:10,110 INFO 31552 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 15:30:10,110 INFO 31552 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 15:30:10,110 INFO 31552 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 15:30:10,110 INFO 31552 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 15:30:10,821 INFO 32384 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js next time will execute after 46789182ms at 2025-08-01 04:30:00.003
2025-07-31 15:30:10,821 INFO 32384 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js next time will execute after 30589179ms at 2025-08-01 00:00:00.000
2025-07-31 15:30:10,821 INFO 32384 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js next time will execute after 30590179ms at 2025-08-01 00:00:01.000
2025-07-31 15:30:10,822 INFO 32384 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 64489179ms at 2025-08-01 09:25:00.001
2025-07-31 17:00:51,657 INFO 35160 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 17:00:51,658 INFO 35160 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 17:00:51,658 INFO 35160 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 17:00:51,658 INFO 35160 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-07-31 17:00:51,837 INFO 31180 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-07-31 17:00:51,837 INFO 31180 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-07-31 17:00:51,837 INFO 31180 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-07-31 17:00:51,837 INFO 31180 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 08:48:20,636 INFO 10560 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 08:48:20,637 INFO 10560 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 08:48:20,637 INFO 10560 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 08:48:20,637 INFO 10560 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 08:48:21,331 INFO 5592 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js next time will execute after 70898672ms at 2025-08-02 04:30:00.002
2025-08-01 08:48:21,331 INFO 5592 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js next time will execute after 54698669ms at 2025-08-02 00:00:00.000
2025-08-01 08:48:21,331 INFO 5592 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js next time will execute after 54699669ms at 2025-08-02 00:00:01.000
2025-08-01 08:48:21,332 INFO 5592 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 2198669ms at 2025-08-01 09:25:00.001
2025-08-01 08:56:09,305 INFO 25340 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 08:56:09,305 INFO 25340 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 08:56:09,305 INFO 25340 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 08:56:09,305 INFO 25340 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 08:57:03,611 INFO 23640 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 08:57:03,611 INFO 23640 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 08:57:03,611 INFO 23640 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 08:57:03,611 INFO 23640 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 08:57:12,972 INFO 26836 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 08:57:12,972 INFO 26836 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 08:57:12,972 INFO 26836 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 08:57:12,972 INFO 26836 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 08:57:27,148 INFO 22384 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 08:57:27,148 INFO 22384 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 08:57:27,148 INFO 22384 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 08:57:27,148 INFO 22384 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 08:59:23,310 INFO 25976 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 08:59:23,311 INFO 25976 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 08:59:23,311 INFO 25976 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 08:59:23,311 INFO 25976 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 08:59:27,399 INFO 1896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 08:59:27,399 INFO 1896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 08:59:27,399 INFO 1896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 08:59:27,399 INFO 1896 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 09:25:00,021 INFO 5592 [Timer] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js next time will execute after 259199981ms at 2025-08-04 09:25:00.002
2025-08-01 09:25:00,020 INFO 1896 [Job#175401150001930481522355001] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js executing by app
2025-08-01 09:25:00,022 ERROR 1896 [Job#175401150001930481522355001] C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js execute failed, used 0ms. TypeError: Cannot read properties of undefined (reading 'get_report_result')
    at Object.task (C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js:10:29)
    at C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-schedule\app.js:52:31
    at AsyncLocalStorage.run (node:async_hooks:346:14)
    at Messenger.<anonymous> (C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-schedule\app.js:51:28)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-08-01 09:26:02,011 INFO 12252 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 09:26:02,011 INFO 12252 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 09:26:02,011 INFO 12252 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 09:26:02,011 INFO 12252 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 10:04:51,308 INFO 21432 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 10:04:51,308 INFO 21432 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 10:04:51,308 INFO 21432 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 10:04:51,308 INFO 21432 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 11:09:20,444 INFO 29548 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 11:09:20,444 INFO 29548 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 11:09:20,444 INFO 29548 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 11:09:20,444 INFO 29548 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 11:09:42,905 INFO 3008 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 11:09:42,906 INFO 3008 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 11:09:42,906 INFO 3008 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 11:09:42,906 INFO 3008 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 11:10:05,355 INFO 33516 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 11:10:05,355 INFO 33516 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 11:10:05,355 INFO 33516 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 11:10:05,355 INFO 33516 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 11:10:49,905 INFO 33376 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 11:10:49,906 INFO 33376 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 11:10:49,906 INFO 33376 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 11:10:49,906 INFO 33376 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 11:24:46,652 INFO 16692 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 11:24:46,652 INFO 16692 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 11:24:46,652 INFO 16692 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 11:24:46,652 INFO 16692 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 11:29:12,889 INFO 26292 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 11:29:12,890 INFO 26292 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 11:29:12,890 INFO 26292 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 11:29:12,890 INFO 26292 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 15:20:43,187 INFO 33188 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 15:20:43,188 INFO 33188 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 15:20:43,188 INFO 33188 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 15:20:43,188 INFO 33188 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 15:26:08,691 INFO 28572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 15:26:08,691 INFO 28572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 15:26:08,691 INFO 28572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 15:26:08,691 INFO 28572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 15:27:26,782 INFO 33156 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 15:27:26,782 INFO 33156 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 15:27:26,782 INFO 33156 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 15:27:26,782 INFO 33156 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 15:27:51,870 INFO 4572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 15:27:51,870 INFO 4572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 15:27:51,870 INFO 4572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 15:27:51,870 INFO 4572 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 15:29:05,579 INFO 4780 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 15:29:05,579 INFO 4780 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 15:29:05,579 INFO 4780 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 15:29:05,579 INFO 4780 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 15:29:08,002 INFO 28528 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 15:29:08,002 INFO 28528 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 15:29:08,002 INFO 28528 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 15:29:08,002 INFO 28528 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
2025-08-01 15:29:30,965 INFO 6352 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-multipart\app\schedule\clean_tmpdir.js
2025-08-01 15:29:30,965 INFO 6352 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\clean_log.js
2025-08-01 15:29:30,965 INFO 6352 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\node_modules\egg-logrotator\app\schedule\rotate_by_file.js
2025-08-01 15:29:30,965 INFO 6352 [egg-schedule]: register schedule C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js
