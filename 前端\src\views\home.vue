<template>
  <div class="page">
    <div class="map h-[100vh] w-[100vw]" id="map"></div>

    <!-- 搜索框 -->
    <div class="search-container fixed top-2 left-2 right-2 z-10">
      <van-search v-model="searchKeyword" placeholder="搜索地址..." :clearable="false" @search="onSearchAddress"
        @input="onSearchAddress" show-action>
        <template #action>
          <van-button size="small" @click="clearSearch">清除</van-button>
        </template>
      </van-search>

      <!-- 搜索结果列表 -->
      <van-cell-group v-if="addressResults.length > 0" class="search-results">
        <van-cell v-for="(result, index) in addressResults" :key="index" :title="result.title" :label="result.address"
          clickable @click="selectAddress(result)">
          <template #right-icon>
            <van-icon name="location-o" color="#1989fa" />
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <div class="font fixed right-0.5 bottom-50 z-10 flex flex-col">
      <div class="pb-2">
        <van-button @click="getCurrentLocation" type="success" icon="location-o" :loading="loading"
          loading-text="定位中...">获取定位</van-button>
      </div>
      <div class="pb-2">
        <MarkerManager :markers="markers" ref="MarkerManagerRef" @update-markers="updateMarkers"
          @locate-to-marker="locateToMarker" @display-markers="displayMarkers" />
      </div>
      <div class="pb-2">
        <van-button @click="setSetting" type="primary" icon="setting-o" class="w-full">设置</van-button>
      </div>
      <van-button @click="logout" color="#ff5c6f" type="danger" icon="cross">退出</van-button>
    </div>

    <!-- 设置弹窗 -->
    <van-popup v-model:show="showSettingPopup" position="bottom" :style="{ height: '60%' }">
      <div class="setting-popup">
        <van-nav-bar title="选择中心城市" @click-right="addCity">
          <template #right>
            <van-button type="primary" size="small">添加城市</van-button>
          </template>
        </van-nav-bar>

        <van-cell-group>
          <van-cell v-for="(city, index) in zoom_list" :key="index" :title="city.city || city.district || '未知城市'"
            :label="`${city.province} ${city.district}`" clickable @click="selectCity(index, city.get_location)">
            <template #right-icon>
              <div class="flex items-center gap-2">
                <van-tag size="large" v-if="city.get_location" type="primary">当前定位</van-tag>
                <van-tag size="large" v-if="city.active === '1' && !city.get_location" type="success">默认</van-tag>

                <van-button v-if="zoom_list.length > 1" type="danger" size="mini" plain @click.stop="removeCity(index)">
                  删除
                </van-button>
              </div>
            </template>
          </van-cell>
        </van-cell-group>

        <div class="p-4">
          <van-button type="primary" block @click="closeSettingPopup">确定</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 添加城市弹窗 -->
    <van-popup v-model:show="showAddCityPopup" position="center" :style="{ width: '90%' }">
      <div class="add-city-popup">
        <van-nav-bar title="添加城市" />

        <div class="p-4">
          <van-search v-model="searchCityName" placeholder="请输入城市名称搜索" @search="onSearchCity" @input="onSearchCity" />

          <van-cell-group v-if="searchResults.length > 0" class="mt-4">
            <van-cell v-for="(result, index) in searchResults" :key="index"
              :label="`${result.province} ${result.city} ${result.district}`" clickable :title="result.name"
              @click="addCityFromSearch(result)" />
          </van-cell-group>

          <div class="mt-4 flex gap-2">
            <van-button @click="closeAddCityPopup" block>取消</van-button>
            <van-button type="primary" @click="closeAddCityPopup" block>确定</van-button>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 标点信息编辑弹窗 - 仅用于地图点击添加标点 -->
    <van-popup v-model:show="showMarkerPopup" position="center" :style="{ width: '90%' }">
      <div class="marker-popup">
        <van-nav-bar title="添加标点" />

        <div class="p-4">
          <van-field v-model="currentMarker.name" label="名称" placeholder="请输入标点名称" required />

          <van-field v-model="currentMarker.description" label="描述" placeholder="请输入标点描述" type="textarea" rows="3" />

          <van-field v-model="currentMarker.address" label="地址" placeholder="地址信息" readonly />

          <div class="mt-4 flex gap-2">
            <van-button @click="closeMarkerPopup" block>取消</van-button>
            <van-button type="primary" @click="saveMarker" block>保存</van-button>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import * as api from '@/api/index'
import MarkerManager from '@/components/MarkerManager.vue'
import { isPC } from '@/utils/tools'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'


//中心城市列表
interface ZOOMLIST {
  province: string,
  city: string,
  district: string,
  lng: number,
  lat: number,
  active?: string,
  get_location?: boolean,
  id?: number,
  name?: string
}
const zoom_list = ref<ZOOMLIST[]>([])

//标点
interface MARKERS {
  name: string,
  description: string,
  address: string,
  lng: number,
  lat: number,
  id?: number
}
const markers = ref<MARKERS[]>([])

// 弹窗控制
const showSettingPopup = ref(false)
const showAddCityPopup = ref(false)
const showMarkerPopup = ref(false)

// 搜索相关
const searchCityName = ref('')
const searchResults = ref<ZOOMLIST[]>([])
const searchKeyword = ref('')
const addressResults = ref<any[]>([])

// 标点相关 - 仅用于地图点击添加标点
const currentMarker = ref<MARKERS>({
  name: '',
  description: '',
  address: '',
  lng: 0,
  lat: 0
})

const userStore = useUserStore()

const router = useRouter()


// 退出登录
const logout = async () => {
  await userStore.logout()
  // 跳转到登录页
  router.push('/login')

}

// dom加载完成
onMounted(async () => {
  await getMyData()
  init()
});



// 获取用户数据,城市列表/定位点等
const getMyData = async () => {
  // 获取城市
  let citys = await api.cityslist({
    limit: 10,
    offset: 1
  })
  zoom_list.value = citys.data.data
  // 获取定位点
  let locations = await api.locationlist({
    limit: 10,
    offset: 1
  })
  markers.value = locations.data.data

  if (!citys.data.data.length || !citys.data.data.find((i: any) => i.active === '1')) {
    //没有城市列表时自动获取中心点
    getCurrentLocation('no-default')
  }
}

// 清理地图实例
onBeforeUnmount(() => {
  if (map) {
    map.destroy();
    map = null;
  }
});


// 初始化
let map: any = null
const init = () => {
  map = new BMapGL.Map("map");
  let active_zoom = zoom_list.value.find(i => i.active === '1')
  if (active_zoom) {
    setCenter({ lng: active_zoom.lng, lat: active_zoom.lat })
  }
  map.enableScrollWheelZoom();     //开启鼠标滚轮缩放

  // 添加地图点击事件
  map.addEventListener('click', onMapClick)

  let scaleCtrl = new BMapGL.ScaleControl();  // 添加比例尺控件
  map.addControl(scaleCtrl);
  let zoomCtrl = new BMapGL.ZoomControl();  // 添加缩放控件
  map.addControl(zoomCtrl);
  // let cityCtrl = new BMapGL.CityListControl();  // 添加城市列表控件
  // map.addControl(cityCtrl);
  // 显示已有标点
  displayMarkers()
}

// 地图点击事件
const onMapClick = async (e: any) => {

  // 检查是否点击在标点上，如果是则不处理
  if (e.overlay) {
    return
  }

  const point = e.latlng

  try {
    // 获取点击位置的地址信息
    const addressInfo: any = await reverseGeocoding({ lng: point.lng, lat: point.lat })
    console.log(addressInfo);

    const address = `${addressInfo.province}${addressInfo.city}${addressInfo.district}${addressInfo.street || ''}${addressInfo.streetNumber || ''}`

    // 设置当前标点信息
    currentMarker.value = {
      name: addressInfo.address_name,
      description: '',
      address: address,
      lng: point.lng,
      lat: point.lat
    }

    showMarkerPopup.value = true
  } catch (error) {
    console.error('获取地址信息失败:', error)
    // 即使获取地址失败，也允许添加标点
    currentMarker.value = {
      name: '',
      description: '',
      address: '未知地址',
      lng: point.lng,
      lat: point.lat
    }

    showMarkerPopup.value = true
  }
}

// 显示标点
const MarkerManagerRef = ref<InstanceType<typeof MarkerManager> | null>(null)
const displayMarkers = () => {
  // 清除现有标点
  map.clearOverlays()

  // 添加所有标点
  markers.value.forEach((marker: any, index) => {
    const point = new (window as any).BMapGL.Point(marker.lng, marker.lat)
    const mapMarker = new (window as any).BMapGL.Marker(point)

    // 添加点击事件 - 点击标点时阻止地图点击事件
    mapMarker.addEventListener('click', (e: any) => {

      if (isPC()) {
        // PC端阻止事件冒泡，避免触发地图点击事件
        e.domEvent.stopPropagation()
      }

      // 调用子组件的editMarker方法
      MarkerManagerRef.value?.editMarker(index)
    })

    // 添加标签
    if (marker.name) {
      const label = new (window as any).BMapGL.Label(marker.name, {
        offset: new (window as any).BMapGL.Size(10, -50)
      })
      label.setStyle({
        color: '#fff',
        fontSize: '16px',
        backgroundColor: '#ffa61b',
        border: '1px solid #ffa61b',
        borderRadius: '3px',
        padding: '2px 5px'
      })
      mapMarker.setLabel(label)
    }

    map.addOverlay(mapMarker)
  })
}

// 设置地图中心点
const setCenter = (point: { lng: number, lat: number }) => {
  map.centerAndZoom(point, 15);
}

// 更新标点列表
const updateMarkers = (newMarkers: MARKERS[]) => {
  markers.value = newMarkers
}

// 定位到标点
const locateToMarker = (marker: MARKERS) => {
  setCenter({ lng: marker.lng, lat: marker.lat })
}

// 获取当前定位
const loading = ref(false)
const getCurrentLocation = (type = '') => {
  loading.value = true
  const geolocation = new BMapGL.Geolocation();
  geolocation.getCurrentPosition(async function (this: any, r: any) {
    if (this.getStatus() == BMAP_STATUS_SUCCESS) {
      //获取当前定位逆地址解析
      let zoom_info: any = await reverseGeocoding({ lng: r.point.lng, lat: r.point.lat })
      zoom_list.value = zoom_list.value.filter(i => !i.get_location)
      zoom_list.value.forEach(i => i.active = '0')
      zoom_list.value.push({
        ...zoom_info,
        lng: r.point.lng,
        lat: r.point.lat,
        active: '1',
        get_location: true, //用户手动获取的定位
      })
      setCenter({ lng: r.point.lng, lat: r.point.lat })
    }
    else {
      alert('failed' + this.getStatus());
    }
    loading.value = false
  });
}

// 逆地址解析
const reverseGeocoding = (location_obj: { lng: number, lat: number }) => {
  const point = new BMapGL.Point(location_obj.lng, location_obj.lat);
  const gc = new BMapGL.Geocoder();
  return new Promise((resolve, reject) => {
    gc.getLocation(point, function (rs: any) {
      if (rs) {
        resolve({
          ...rs.addressComponents,
          address_name: rs.surroundingPois[0]?.title || ''
        })
      } else {
        reject(rs)
      }
    });
  })
}

// 设置功能
const setSetting = () => {
  showSettingPopup.value = true
}

// 关闭设置弹窗
const closeSettingPopup = () => {
  showSettingPopup.value = false
}

// 选择城市
const selectCity = async (index: number, get_location?: boolean) => {
  // 将所有城市设为非激活状态
  zoom_list.value.forEach(city => city.active = '0')
  // 设置选中的城市为激活状态
  zoom_list.value[index].active = '1'

  if (get_location) {
    let info = await api.cityssave(zoom_list.value[index])
    zoom_list.value[index].id = info.data.id
  }

  if (zoom_list.value[index].id) {
    await api.citySetDefault({
      id: zoom_list.value[index].id
    })
  }

  getMyData()
  // 重新初始化地图
  setCenter({ lng: zoom_list.value[index].lng, lat: zoom_list.value[index].lat })
  closeSettingPopup()
}

// 删除城市
const removeCity = async (index: number) => {
  if (zoom_list.value.length == 0) return

  const cityId = zoom_list.value[index].id
  if (cityId) {
    await api.citysdel({
      id: cityId
    })
  }

  // 重新获取
  getMyData()
}

// 添加城市
const addCity = () => {
  showAddCityPopup.value = true
  searchCityName.value = ''
  searchResults.value = []
}

// 关闭添加城市弹窗
const closeAddCityPopup = () => {
  showAddCityPopup.value = false
}


// 搜索城市
const onSearchCity = async () => {
  if (!searchCityName.value.trim()) {
    searchResults.value = []
    return
  }

  try {
    // 使用百度地图API搜索城市
    const local = new BMapGL.LocalSearch(map, {
      onSearchComplete: function (results: any) {
        if (local.getStatus() == BMAP_STATUS_SUCCESS) {
          const results_list = []
          for (let i = 0; i < results.getCurrentNumPois(); i++) {
            const poi = results.getPoi(i)
            results_list.push({
              name: poi.title,
              province: poi.province || '',
              city: poi.city || '',
              district: poi.district || '',
              lng: poi.point.lng,
              lat: poi.point.lat
            })
          }
          searchResults.value = results_list
        }
      }
    })
    local.search(searchCityName.value)
  } catch (error) {
    console.error('搜索城市失败:', error)
  }
}

// 从搜索结果添加城市
const addCityFromSearch = async (result: any) => {
  try {
    // 获取详细地址信息
    const addressInfo: any = await reverseGeocoding({ lng: result.lng, lat: result.lat })

    // 检查是否已存在相同城市
    const exists = zoom_list.value.some(city =>
      city.lng === result.lng && city.lat === result.lat
    )

    if (!exists) {

      // 存储到接口中
      await api.cityssave({
        name: result.name,
        province: addressInfo.province,
        city: addressInfo.city,
        district: addressInfo.district,
        lng: result.lng,
        lat: result.lat
      })
      // 添加新城市并设为激活状态
      zoom_list.value.push({
        ...addressInfo,
        lng: result.lng,
        lat: result.lat,
        active: '0'
      })

      // 重新初始化地图
      // setCenter({ lng: result.lng, lat: result.lat })
    }

    // 关闭弹窗
    closeAddCityPopup()
  } catch (error) {
    console.error('添加城市失败:', error)
  }
}

// 搜索地址
const onSearchAddress = async () => {
  if (!searchKeyword.value.trim()) {
    addressResults.value = []
    return
  }

  try {
    const activeCity = zoom_list.value.find(city => city.active === '1')
    if (!activeCity) return

    // 在当前城市范围内搜索
    const local = new BMapGL.LocalSearch(map, {
      renderOptions: {
        map: map,
      },
      onSearchComplete: function (results: any) {
        if (local.getStatus() == BMAP_STATUS_SUCCESS) {
          const results_list = []
          for (let i = 0; i < results.getCurrentNumPois(); i++) {
            const poi = results.getPoi(i)
            results_list.push({
              title: poi.title,
              address: poi.address,
              lng: poi.point.lng,
              lat: poi.point.lat
            })
          }
          addressResults.value = results_list
        }
      }
    })
    local.search(searchKeyword.value)
  } catch (error) {
    console.error('搜索地址失败:', error)
  }
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  addressResults.value = []
}

// 选择地址并添加标点
const selectAddress = (result: any) => {
  // 清除搜索结果
  addressResults.value = []
  searchKeyword.value = ''

  // 将地图中心移动到选中的地址
  const point = new BMapGL.Point(result.lng, result.lat)
  map.centerAndZoom(point, 16)

  // 直接添加标记点，不弹出编辑框
  const newMarker = {
    name: result.title,
    description: '',
    address: result.address,
    lng: result.lng,
    lat: result.lat
  }

  markers.value.push(newMarker)

  // 重新显示标点
  displayMarkers()
}

// 保存标点 - 仅用于地图点击添加标点
const saveMarker = () => {
  if (!currentMarker.value.name.trim()) {
    alert('请输入标点名称')
    return
  }

  // 添加新标点
  markers.value.push({ ...currentMarker.value })
  // 接口保存
  api.locationsave(currentMarker.value)

  // 重新显示标点
  displayMarkers()

  // 关闭弹窗
  closeMarkerPopup()
}

// 关闭标点弹窗
const closeMarkerPopup = () => {
  showMarkerPopup.value = false
  currentMarker.value = {
    name: '',
    description: '',
    address: '',
    lng: 0,
    lat: 0
  }
}

// 计算属性
computed(() => { });


//页面卸载
onBeforeUnmount(() => { });

</script>

<style scoped lang="scss">
::v-deep(.van-search__action) {
  display: flex;
  align-items: center;

  .van-button__text {
    font-size: 14px;
  }
}

.search-container {
  .search-results {
    max-height: 600px;
    overflow-y: auto;
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e8e8e8;
    border-top: none;

    :deep(.van-cell) {
      &:hover {
        background-color: #f5f5f5;
      }

      .van-cell__title {
        font-weight: 500;
        color: #333;
      }

      .van-cell__label {
        color: #666;
        font-size: 12px;
      }
    }
  }
}

// 媒体查询
@media (min-width: 768px) {
  .search-results {
    max-height: 600px;
  }
}

// 移动端适配
@media (max-width: 480px) {
  .search-results {
    max-height: 1500px;
    min-height: 1500px;
  }
}

.setting-popup {
  height: 100%;
  display: flex;
  flex-direction: column;

  .van-cell-group {
    flex: 1;
    overflow-y: auto;
  }
}

.add-city-popup {
  .van-cell-group {
    max-height: 300px;
    overflow-y: auto;
  }
}

.marker-popup {
  .van-field {
    margin-bottom: 12px;
  }
}

// 激活城市的样式
:deep(.van-cell.active-city) {
  background-color: #f0f9ff;
}
</style>
