// This file is created by egg-ts-helper@2.1.1
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
import ExportCitys = require('../../../app/model/citys');
import ExportLocation = require('../../../app/model/location');
import ExportPaths = require('../../../app/model/paths');
import ExportUser = require('../../../app/model/user');

declare module 'egg' {
  interface IModel {
    Citys: ReturnType<typeof ExportCitys>;
    Location: ReturnType<typeof ExportLocation>;
    Paths: ReturnType<typeof ExportPaths>;
    User: ReturnType<typeof ExportUser>;
  }
}
