<template>
  <div class="plan-container">
    <!-- 头部操作区 -->
    <div class="header-actions">
      <van-button type="primary" @click="showCreateDialog = true" size="small">
        新建路径规划
      </van-button>
    </div>

    <!-- 路径规划列表 -->
    <div class="plans-list">
      <van-empty v-if="!plansList.length" description="暂无路径规划" />
      <div v-else class="plan-items">
        <div v-for="plan in plansList" :key="plan.id" class="plan-item" @click="viewPlanDetail(plan)">
          <div class="plan-header">
            <h3 class="plan-title">{{ plan.title }}</h3>
            <div class="plan-actions">
              <van-button type="primary" size="mini" @click.stop="editPlan(plan)">
                编辑
              </van-button>
              <van-button type="danger" size="mini" @click.stop="deletePlan(plan.id!)">
                删除
              </van-button>
            </div>
          </div>
          <div class="plan-info">
            <span class="plan-code">编号: {{ plan.code }}</span>
            <span class="plan-time">{{ plan.time }}</span>
          </div>
          <div class="plan-locations">
            <span class="locations-count">
              包含 {{ getPlanLocationsCount(plan.jsons) }} 个地点
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑路径规划弹窗 -->
    <van-dialog v-model:show="showCreateDialog" :title="editingPlan ? '编辑路径规划' : '新建路径规划'" show-cancel-button
      @confirm="savePlan" @cancel="resetForm" :close-on-click-overlay="false" class="plan-dialog">
      <div class="form-content">
        <!-- 路径名称 -->
        <van-field v-model="formData.title" label="路径名称" placeholder="请输入路径名称" required />

        <!-- 地点选择区域 -->
        <div class="locations-section">
          <div class="section-title">选择地点并排序</div>

          <!-- 可选地点列表 -->
          <div class="available-locations">
            <div class="subtitle">可选地点:</div>
            <div class="location-chips">
              <van-tag v-for="location in availableLocations" :key="location.id"
                :type="selectedLocationIds.includes(location.id) ? 'primary' : 'default'"
                @click="toggleLocation(location)" class="location-chip">
                {{ location.name }}
              </van-tag>
            </div>
          </div>

          <!-- 已选地点排序 -->
          <div class="selected-locations" v-if="selectedLocations.length">
            <div class="subtitle">路径顺序 (可拖拽排序):</div>
            <div class="sorted-locations">
              <div v-for="(location, index) in selectedLocations" :key="location.id" class="sorted-location-item"
                @click="removeLocation(index)">
                <span class="location-order">{{ index + 1 }}</span>
                <span class="location-name">{{ location.name }}</span>
                <van-icon name="cross" class="remove-icon" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 路径详情弹窗 -->
    <van-dialog v-model:show="showDetailDialog" :title="currentPlan?.title || '路径详情'" show-cancel-button
      confirm-button-text="关闭" :show-confirm-button="false" cancel-button-text="关闭" class="detail-dialog">
      <div class="detail-content" v-if="currentPlan">
        <div class="detail-info">
          <p><strong>编号:</strong> {{ currentPlan.code }}</p>
          <p><strong>创建时间:</strong> {{ currentPlan.time }}</p>
        </div>
        <div class="detail-locations">
          <h4>路径地点:</h4>
          <div class="location-list">
            <div v-for="(location, index) in getPlanLocations(currentPlan.jsons)" :key="location.id"
              class="location-detail-item">
              <span class="location-index">{{ index + 1 }}</span>
              <div class="location-detail">
                <div class="location-name">{{ location.name }}</div>
                <div class="location-address">{{ location.address }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { showToast, showConfirmDialog } from "vant";
import { locationlist } from "@/api/locations";
import { pathslist, pathssave, pathsdel } from "@/api/paths";

// 接口类型定义
interface Location {
  id: number;
  name: string;
  description: string;
  address: string;
  lng: string;
  lat: string;
  time: string;
  uid: number;
}

interface Plan {
  id?: number;
  code: string;
  title: string;
  jsons: string;
  time: string;
  uid: number;
}

// 响应式数据
const plansList = ref<Plan[]>([]);
const availableLocations = ref<Location[]>([]);
const selectedLocations = ref<Location[]>([]);
const selectedLocationIds = computed(() => selectedLocations.value.map(loc => loc.id));

// 弹窗控制
const showCreateDialog = ref(false);
const showDetailDialog = ref(false);
const editingPlan = ref<Plan | null>(null);
const currentPlan = ref<Plan | null>(null);

// 表单数据
const formData = ref({
  title: "",
});

// 生成随机编号
const generateCode = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 7);
  return `PATH_${timestamp}_${random}`.toUpperCase();
};

// 获取路径规划列表
const loadPlansList = async () => {
  try {
    const response = await pathslist({
      limit: 100,
      offset: 1,
    });
    if (response.success) {
      plansList.value = response.data.data || [];
    }
  } catch (error) {
    showToast("获取路径规划列表失败");
  }
};

// 获取可用地点列表
const loadAvailableLocations = async () => {
  try {
    const response = await locationlist({
      limit: 100,
      offset: 1,
    });
    if (response.success) {
      availableLocations.value = response.data.data || [];
    }
  } catch (error) {
    showToast("获取地点列表失败");
  }
};

// 切换地点选择
const toggleLocation = (location: Location) => {
  const index = selectedLocations.value.findIndex(loc => loc.id === location.id);
  if (index > -1) {
    selectedLocations.value.splice(index, 1);
  } else {
    selectedLocations.value.push(location);
  }
};

// 移除已选地点
const removeLocation = (index: number) => {
  selectedLocations.value.splice(index, 1);
};

// 获取路径中的地点数量
const getPlanLocationsCount = (jsons: string): number => {
  try {
    const locations = JSON.parse(jsons || "[]");
    return Array.isArray(locations) ? locations.length : 0;
  } catch {
    return 0;
  }
};

// 获取路径中的地点列表
const getPlanLocations = (jsons: string): Location[] => {
  try {
    const locations = JSON.parse(jsons || "[]");
    return Array.isArray(locations) ? locations : [];
  } catch {
    return [];
  }
};

// 保存路径规划
const savePlan = async () => {
  if (!formData.value.title.trim()) {
    showToast("请输入路径名称");
    return;
  }

  if (selectedLocations.value.length === 0) {
    showToast("请至少选择一个地点");
    return;
  }

  try {
    const planData = {
      title: formData.value.title,
      code: editingPlan.value?.code || generateCode(),
      jsons: JSON.stringify(selectedLocations.value),
      ...(editingPlan.value?.id && { id: editingPlan.value.id }),
    };

    const response = await pathssave(planData);
    if (response.success) {
      showToast(editingPlan.value ? "更新成功" : "创建成功");
      showCreateDialog.value = false;
      resetForm();
      loadPlansList();
    } else {
      showToast("保存失败");
    }
  } catch (error) {
    showToast("保存失败");
  }
};

// 编辑路径规划
const editPlan = (plan: Plan) => {
  editingPlan.value = plan;
  formData.value.title = plan.title;
  selectedLocations.value = getPlanLocations(plan.jsons);
  showCreateDialog.value = true;
};

// 删除路径规划
const deletePlan = async (id: number) => {
  try {
    await showConfirmDialog({
      title: "确认删除",
      message: "确定要删除这个路径规划吗？",
    });

    const response = await pathsdel({ id });
    if (response.success) {
      showToast("删除成功");
      loadPlansList();
    } else {
      showToast("删除失败");
    }
  } catch (error) {
    // 用户取消删除
  }
};

// 查看路径详情
const viewPlanDetail = (plan: Plan) => {
  currentPlan.value = plan;
  showDetailDialog.value = true;
};

// 重置表单
const resetForm = () => {
  formData.value.title = "";
  selectedLocations.value = [];
  editingPlan.value = null;
};

// 页面加载完成
onMounted(() => {
  loadPlansList();
  loadAvailableLocations();
});

// 页面卸载
onBeforeUnmount(() => {
  // 清理工作
});
</script>

<style scoped lang="scss">
.plan-container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 弹窗样式
.plan-dialog {
  .form-content {
    padding: 16px;

    .locations-section {
      margin-top: 16px;

      .section-title {
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
      }

      .subtitle {
        color: #666;
        margin-bottom: 8px;
      }

      .available-locations {
        margin-bottom: 16px;

        .location-chips {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .location-chip {
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              transform: scale(1.05);
            }
          }
        }
      }

      .selected-locations {
        .sorted-locations {
          .sorted-location-item {
            display: flex;
            align-items: center;
            background: #f0f8ff;
            border: 1px solid #1989fa;
            border-radius: 6px;
            padding: 8px 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: #e8f4fd;
            }


          }
        }
      }
    }
  }
}

.detail-dialog {
  .detail-content {
    padding: 16px;

    .detail-info {
      margin-bottom: 16px;

      p {
        margin: 8px 0;
        font-size: 14px;
        color: #333;
      }
    }

    .detail-locations {
      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: #333;
      }

      .location-list {
        .location-detail-item {
          display: flex;
          align-items: center;
          padding: 12px;
          background: #f8f9fa;
          border-radius: 6px;
          margin-bottom: 8px;

          .location-index {
            background: #1989fa;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 12px;
          }

          .location-detail {
            flex: 1;

            .location-name {
              font-size: 14px;
              font-weight: 600;
              color: #333;
              margin-bottom: 4px;
            }

            .location-address {
              font-size: 12px;
              color: #666;
            }
          }
        }
      }
    }
  }
}

//PC端
@media (min-width: 481px) {

  .van-dialog{
    
  }

  .header-actions {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .subtitle {
    font-size: 16px
  }

  .section-title {
    font-size: 20px;
  }

  .location-chip {
    font-size: 20px;
    padding: 5px;
    line-height: 25px;
    border-radius: 5px;
  }

  .location-name {
    flex: 1;
    font-size: 20px;
  }

  .remove-icon {
    color: #ff4444;
    font-size: 20px;
  }


  .location-order {
    background: #1989fa;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    margin-right: 8px;
  }

  .plans-list {
    .plan-items {
      .plan-item {
        background: white;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .plan-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .plan-title {
            margin: 0;
            font-size: 25px;
            font-weight: 600;
            color: #333;
          }

          .plan-actions {
            display: flex;
            gap: 8px;

            .van-button {
              padding: 4px 8px;
              font-size: 13px;
            }
          }
        }

        .plan-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          font-size: 12px;
          color: #666;

          .plan-code {
            font-size: 20px;
            font-family: monospace;
            background: #f0f0f0;
            padding: 5px 10px;
            border-radius: 4px;
          }
        }

        .plan-locations {
          font-size: 18px;
          color: #999;

          .locations-count {
            background: #e8f4fd;
            color: #1989fa;
            padding: 5px 10px;
            border-radius: 4px;
          }
        }
      }
    }
  }


}


// 移动端适配
@media (max-width: 480px) {
  .header-actions {
    margin-bottom: 60px;
    display: flex;
    justify-content: flex-end;
  }

  .location-name {
    flex: 1;
    font-size: 80px;
  }

  .remove-icon {
    color: #ff4444;
    font-size: 80px;
  }

  .location-order {
    background: #1989fa;
    color: white;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60px;
    margin-right: 8px;
  }


  .location-chip {
    font-size: 80px;
    padding: 20px;
    line-height: 90px;
    border-radius: 30px;
  }

  .section-title {
    font-size: 70px;
  }

  .subtitle {
    font-size: 65px
  }


  .plans-list {
    .plan-items {
      .plan-item {
        background: white;
        border-radius: 20px;
        padding: 50px;
        margin-bottom: px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .plan-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .plan-title {
            margin: 0;
            font-size: 80px;
            font-weight: 600;
            color: #333;
          }

          .plan-actions {
            display: flex;
            gap: 8px;

            .van-button {
              padding: 4px 8px;
              font-size: 13px;
            }
          }
        }

        .plan-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 40px;
          font-size: 50px;
          color: #666;

          .plan-code {
            font-size: 60px;
            font-family: monospace;
            background: #f0f0f0;
            padding: 20px 40px;
            border-radius: 20px;
          }
        }

        .plan-locations {
          font-size: 50px;
          color: #999;

          .locations-count {
            background: #e8f4fd;
            color: #1989fa;
            padding: 20px 40px;
            border-radius: 20px;
          }
        }
      }
    }
  }



  .plan-container {
    padding: 40px;

    .plans-list .plan-items .plan-item {
      .plan-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .plan-actions {
          align-self: flex-end;
        }
      }
    }
  }

  .plan-dialog .form-content {
    padding: 40px;

    .locations-section {
      .available-locations .location-chips {
        gap: 20px;
      }

      .selected-locations .sorted-locations .sorted-location-item {
        padding: 20px 40px;
      }
    }
  }

  .detail-dialog .detail-content {
    padding: 12px;

    .detail-locations .location-list .location-detail-item {
      padding: 10px;
    }
  }
}
</style>
