{"config": {"session": {"maxAge": 86400000, "key": "EGG_SESS", "httpOnly": true, "encrypt": true, "logValue": true}, "security": {"domainWhiteList": ["http://127.0.0.1:8089"], "protocolWhiteList": [], "defaultMiddleware": "csrf,hsts,methodnoallow,noopen,nosniff,csp,xssProtection,xframe,dta", "csrf": {"enable": false, "type": "ctoken", "ignoreJSON": false, "useSession": false, "cookieName": "csrfToken", "sessionName": "csrfToken", "headerName": "x-csrf-token", "bodyName": "_csrf", "queryName": "_csrf", "rotateWhenInvalid": false, "supportedRequests": [{"path": {}, "methods": ["POST", "PATCH", "DELETE", "PUT", "CONNECT"]}], "refererWhiteList": [], "cookieOptions": {"signed": false}}, "xframe": {"enable": true, "value": "SAMEORIGIN"}, "hsts": {"enable": false, "maxAge": 31536000, "includeSubdomains": false}, "dta": {"enable": true}, "methodnoallow": {"enable": true}, "noopen": {"enable": true}, "nosniff": {"enable": true}, "referrerPolicy": {"enable": false, "value": "no-referrer-when-downgrade"}, "xssProtection": {"enable": true, "value": "1; mode=block"}, "csp": {"enable": false, "policy": {}}, "ssrf": {"ipBlackList": null, "ipExceptionList": null, "hostnameExceptionList": null, "checkAddress": null}, "_protocolWhiteListSet": "<Set>"}, "helper": {"shtml": {}}, "jsonp": {"limit": 50, "callback": ["_callback", "callback"], "csrf": false}, "onerror": {"errorPageUrl": "", "appErrorFilter": null, "templatePath": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-onerror\\lib\\onerror_page.mustache"}, "i18n": {"defaultLocale": "en_US", "dirs": [], "queryField": "locale", "cookieField": "locale", "cookieDomain": "", "cookieMaxAge": "1y"}, "watcher": {"type": "development", "eventSources": {"default": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-watcher\\lib\\event-sources\\default", "development": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-watcher\\lib\\event-sources\\development"}}, "customLogger": {"scheduleLogger": {"consoleLevel": "NONE", "file": "egg-schedule.log"}}, "schedule": {"directory": []}, "multipart": {"mode": "stream", "autoFields": false, "defaultCharset": "utf8", "defaultParamCharset": "utf8", "fieldNameSize": 100, "fieldSize": "100kb", "fields": 10, "fileSize": "10mb", "files": 10, "fileExtensions": [], "whitelist": null, "allowArrayField": false, "tmpdir": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\egg-multipart-tmp\\problem-node", "cleanSchedule": {"cron": "0 30 4 * * *", "disable": false}}, "development": {"watchDirs": [], "ignoreDirs": [], "fastReady": false, "reloadOnDebug": true, "overrideDefault": false, "overrideIgnore": false}, "logrotator": {"filesRotateByHour": null, "hourDelimiter": "-", "filesRotateBySize": null, "maxFileSize": 52428800, "maxFiles": 10, "rotateDuration": 60000, "maxDays": 31}, "static": {"prefix": "/", "dir": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public", "dynamic": true, "preload": false, "buffer": false, "maxFiles": 1000, "maxAge": 31536000}, "view": {"root": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\view", "cache": false, "defaultExtension": ".html", "defaultViewEngine": "nunjucks", "mapping": {".html": "nunjucks"}}, "sequelize": {"dialect": "mysql", "database": "plan-map", "host": "localhost", "port": "3306", "username": "root", "password": "<String len: 4>"}, "cors": {"origin": "*", "allowMethods": "GET, PUT, POST, DELETE, PATCH"}, "nunjucks": {"autoescape": true, "throwOnUndefined": false, "trimBlocks": false, "lstripBlocks": false, "cache": false}, "env": "local", "name": "problem-node", "keys": "<String len: 31>", "cookies": {}, "proxy": false, "maxIpsCount": 0, "maxProxyCount": 0, "protocolHeaders": "x-forwarded-proto", "ipHeaders": "x-forwarded-for", "hostHeaders": "", "pkg": {"name": "problem-node", "version": "1.0.0", "description": "问题记录器", "private": true, "egg": {"declarations": true}, "dependencies": {"axios": "^1.7.7", "egg": "^3.17.5", "egg-cors": "^3.0.1", "egg-schedule": "^4.0.1", "egg-scripts": "2", "egg-sequelize": "^6.0.0", "egg-view-nunjucks": "^2.3.0", "moment": "^2.30.1", "mysql2": "^3.11.3"}, "devDependencies": {"egg-bin": "6", "egg-mock": "5", "eslint": "8", "eslint-config-egg": "13"}, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-problem-node", "stop": "egg-scripts stop --title=egg-server-problem-node", "dev": "egg-bin dev", "test": "npm run lint -- --fix && npm run test:local", "test:local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov"}, "repository": {"type": "git", "url": ""}, "author": "yamlling", "license": "MIT"}, "baseDir": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端", "HOME": "C:\\Users\\<USER>", "rundir": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\run", "dump": {"ignore": "<Set>", "timing": {"slowBootActionMinDuration": 5000}}, "confusedConfigurations": {"bodyparser": "<PERSON><PERSON><PERSON><PERSON>", "notFound": "notfound", "sitefile": "siteFile", "middlewares": "middleware", "httpClient": "httpclient"}, "notfound": {"pageUrl": ""}, "siteFile": {"/favicon.ico": "<Buffer len: 6463>", "cacheControl": "public, max-age=2592000"}, "bodyParser": {"enable": true, "encoding": "utf8", "formLimit": "1mb", "jsonLimit": "1mb", "textLimit": "1mb", "strict": true, "queryString": {"arrayLimit": 100, "depth": 5, "parameterLimit": 1000}, "onerror": "<Function onerror>"}, "logger": {"dir": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node", "encoding": "utf8", "env": "local", "level": "INFO", "consoleLevel": "INFO", "disableConsoleAfterReady": false, "outputJSON": false, "buffer": true, "appLogName": "problem-node-web.log", "coreLogName": "egg-web.log", "agentLogName": "egg-agent.log", "errorLogName": "common-error.log", "coreLogger": {"consoleLevel": "WARN"}, "allowDebugAtProd": false, "enablePerformanceTimer": false, "enableFastContextLogger": false, "type": "agent", "localStorage": "<AsyncLocalStorage>"}, "httpclient": {"enableDNSCache": false, "dnsCacheLookupInterval": 10000, "dnsCacheMaxLength": 1000, "request": {"timeout": 5000}, "httpAgent": {"keepAlive": true, "freeSocketTimeout": 4000, "maxSockets": 9007199254740991, "maxFreeSockets": 256}, "httpsAgent": {"keepAlive": true, "freeSocketTimeout": 4000, "maxSockets": 9007199254740991, "maxFreeSockets": 256}, "useHttpClientNext": false}, "meta": {"enable": true, "logging": false}, "coreMiddleware": ["meta", "siteFile", "notfound", "<PERSON><PERSON><PERSON><PERSON>", "override<PERSON><PERSON><PERSON>"], "workerStartTimeout": 600000, "serverTimeout": null, "cluster": {"listen": {"path": "", "port": 8089, "hostname": ""}}, "clusterClient": {"maxWaitTime": 60000, "responseTimeout": 60000}, "onClientError": null, "middleware": [], "coreMiddlewares": "~config~coreMiddleware", "appMiddlewares": "~config~middleware", "appMiddleware": "~config~middleware"}, "plugins": {"onerror": {"enable": true, "package": "egg-onerror", "name": "onerror", "dependencies": [], "optionalDependencies": ["jsonp"], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-onerror", "version": "2.4.0"}, "session": {"enable": true, "package": "egg-session", "name": "session", "dependencies": [], "optionalDependencies": [], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-session", "version": "3.3.0"}, "i18n": {"enable": true, "package": "egg-i18n", "name": "i18n", "dependencies": [], "optionalDependencies": [], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-i18n", "version": "2.1.1"}, "watcher": {"enable": true, "package": "egg-watcher", "name": "watcher", "dependencies": [], "optionalDependencies": [], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-watcher", "version": "3.1.1", "dependents": ["development"]}, "multipart": {"enable": true, "package": "egg-multipart", "name": "multipart", "dependencies": [], "optionalDependencies": ["schedule"], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-multipart", "version": "3.5.0"}, "security": {"enable": true, "package": "egg-security", "name": "security", "dependencies": [], "optionalDependencies": ["session"], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-security", "version": "3.7.0", "dependents": ["nunjucks"]}, "development": {"enable": true, "package": "egg-development", "name": "development", "dependencies": ["watcher"], "optionalDependencies": [], "env": ["local"], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-development", "version": "3.0.2"}, "logrotator": {"enable": true, "package": "egg-logrotator", "name": "logrotator", "dependencies": ["schedule"], "optionalDependencies": [], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-logrotator", "version": "3.2.0"}, "schedule": {"enable": true, "package": "egg-schedule", "name": "schedule", "dependencies": [], "optionalDependencies": [], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-schedule", "version": "4.0.1", "dependents": ["logrotator"]}, "static": {"enable": true, "package": "egg-static", "name": "static", "dependencies": [], "optionalDependencies": [], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-static", "version": "2.3.1"}, "jsonp": {"enable": true, "package": "egg-jsonp", "name": "jsonp", "dependencies": [], "optionalDependencies": ["security"], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-jsonp", "version": "2.0.0"}, "view": {"enable": true, "package": "egg-view", "name": "view", "dependencies": [], "optionalDependencies": [], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-view", "version": "2.1.4", "dependents": ["nunjucks"]}, "sequelize": {"enable": true, "package": "egg-sequelize", "name": "sequelize", "dependencies": [], "optionalDependencies": [], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-sequelize", "version": "6.0.0"}, "cors": {"enable": true, "package": "egg-cors", "name": "cors", "dependencies": [], "optionalDependencies": [], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-cors", "version": "3.0.1"}, "nunjucks": {"enable": true, "package": "egg-view-nunjucks", "name": "nunjucks", "dependencies": ["security", "view"], "optionalDependencies": [], "env": [], "from": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\plugin.js", "path": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\node_modules\\egg-view-nunjucks", "version": "2.3.0"}}, "appInfo": {"name": "problem-node", "baseDir": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端", "env": "local", "scope": "", "HOME": "C:\\Users\\<USER>", "pkg": {"name": "problem-node", "version": "1.0.0", "description": "问题记录器", "private": true, "egg": {"declarations": true}, "dependencies": {"axios": "^1.7.7", "egg": "^3.17.5", "egg-cors": "^3.0.1", "egg-schedule": "^4.0.1", "egg-scripts": "2", "egg-sequelize": "^6.0.0", "egg-view-nunjucks": "^2.3.0", "moment": "^2.30.1", "mysql2": "^3.11.3"}, "devDependencies": {"egg-bin": "6", "egg-mock": "5", "eslint": "8", "eslint-config-egg": "13"}, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-problem-node", "stop": "egg-scripts stop --title=egg-server-problem-node", "dev": "egg-bin dev", "test": "npm run lint -- --fix && npm run test:local", "test:local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov"}, "repository": {"type": "git", "url": ""}, "author": "yamlling", "license": "MIT"}, "root": "C:\\Users\\<USER>\\Desktop\\plan-map\\后端"}}