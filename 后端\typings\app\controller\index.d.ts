// This file is created by egg-ts-helper@2.1.1
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
import ExportCitys = require('../../../app/controller/citys');
import ExportLocation = require('../../../app/controller/location');
import ExportPaths = require('../../../app/controller/paths');
import ExportUser = require('../../../app/controller/user');

declare module 'egg' {
  interface IController {
    citys: ExportCitys;
    location: ExportLocation;
    paths: ExportPaths;
    user: ExportUser;
  }
}
