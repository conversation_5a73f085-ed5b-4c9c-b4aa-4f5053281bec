[{"name": "Process Start", "start": 1754033393215, "end": 1754033394256, "duration": 1041, "pid": 23380, "index": 0}, {"name": "Application Start", "start": 1754033394257, "end": 1754033395266, "duration": 1009, "pid": 23380, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1754033394276, "end": 1754033394300, "duration": 24, "pid": 23380, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1754033394300, "end": 1754033394332, "duration": 32, "pid": 23380, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1754033394301, "end": 1754033394301, "duration": 0, "pid": 23380, "index": 4}, {"name": "Require(1) node_modules/egg-session/config/config.default.js", "start": 1754033394303, "end": 1754033394304, "duration": 1, "pid": 23380, "index": 5}, {"name": "Require(2) node_modules/egg-security/config/config.default.js", "start": 1754033394304, "end": 1754033394305, "duration": 1, "pid": 23380, "index": 6}, {"name": "Require(3) node_modules/egg-jsonp/config/config.default.js", "start": 1754033394306, "end": 1754033394306, "duration": 0, "pid": 23380, "index": 7}, {"name": "Require(4) node_modules/egg-onerror/config/config.default.js", "start": 1754033394307, "end": 1754033394308, "duration": 1, "pid": 23380, "index": 8}, {"name": "Require(5) node_modules/egg-i18n/config/config.default.js", "start": 1754033394308, "end": 1754033394309, "duration": 1, "pid": 23380, "index": 9}, {"name": "Require(6) node_modules/egg-watcher/config/config.default.js", "start": 1754033394309, "end": 1754033394310, "duration": 1, "pid": 23380, "index": 10}, {"name": "Require(7) node_modules/egg-schedule/config/config.default.js", "start": 1754033394310, "end": 1754033394311, "duration": 1, "pid": 23380, "index": 11}, {"name": "Require(8) node_modules/egg-multipart/config/config.default.js", "start": 1754033394312, "end": 1754033394312, "duration": 0, "pid": 23380, "index": 12}, {"name": "Require(9) node_modules/egg-development/config/config.default.js", "start": 1754033394313, "end": 1754033394313, "duration": 0, "pid": 23380, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1754033394314, "end": 1754033394314, "duration": 0, "pid": 23380, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1754033394315, "end": 1754033394317, "duration": 2, "pid": 23380, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1754033394318, "end": 1754033394318, "duration": 0, "pid": 23380, "index": 16}, {"name": "Require(13) node_modules/egg-sequelize/config/config.default.js", "start": 1754033394319, "end": 1754033394319, "duration": 0, "pid": 23380, "index": 17}, {"name": "Require(14) node_modules/egg-cors/config/config.default.js", "start": 1754033394320, "end": 1754033394320, "duration": 0, "pid": 23380, "index": 18}, {"name": "Require(15) node_modules/egg-view-nunjucks/config/config.default.js", "start": 1754033394321, "end": 1754033394321, "duration": 0, "pid": 23380, "index": 19}, {"name": "Require(16) node_modules/egg/config/config.default.js", "start": 1754033394322, "end": 1754033394322, "duration": 0, "pid": 23380, "index": 20}, {"name": "Require(17) config/config.default.js", "start": 1754033394324, "end": 1754033394324, "duration": 0, "pid": 23380, "index": 21}, {"name": "Require(18) node_modules/egg-security/config/config.local.js", "start": 1754033394325, "end": 1754033394325, "duration": 0, "pid": 23380, "index": 22}, {"name": "Require(19) node_modules/egg-watcher/config/config.local.js", "start": 1754033394326, "end": 1754033394327, "duration": 1, "pid": 23380, "index": 23}, {"name": "Require(20) node_modules/egg-view/config/config.local.js", "start": 1754033394328, "end": 1754033394329, "duration": 1, "pid": 23380, "index": 24}, {"name": "Require(21) node_modules/egg-view-nunjucks/config/config.local.js", "start": 1754033394330, "end": 1754033394330, "duration": 0, "pid": 23380, "index": 25}, {"name": "Require(22) node_modules/egg/config/config.local.js", "start": 1754033394331, "end": 1754033394331, "duration": 0, "pid": 23380, "index": 26}, {"name": "Load extend/application.js", "start": 1754033394333, "end": 1754033394392, "duration": 59, "pid": 23380, "index": 27}, {"name": "Require(23) node_modules/egg-session/app/extend/application.js", "start": 1754033394334, "end": 1754033394335, "duration": 1, "pid": 23380, "index": 28}, {"name": "Require(24) node_modules/egg-security/app/extend/application.js", "start": 1754033394336, "end": 1754033394338, "duration": 2, "pid": 23380, "index": 29}, {"name": "Require(25) node_modules/egg-jsonp/app/extend/application.js", "start": 1754033394339, "end": 1754033394342, "duration": 3, "pid": 23380, "index": 30}, {"name": "Require(26) node_modules/egg-schedule/app/extend/application.js", "start": 1754033394343, "end": 1754033394347, "duration": 4, "pid": 23380, "index": 31}, {"name": "Require(27) node_modules/egg-logrotator/app/extend/application.js", "start": 1754033394348, "end": 1754033394350, "duration": 2, "pid": 23380, "index": 32}, {"name": "Require(28) node_modules/egg-view/app/extend/application.js", "start": 1754033394351, "end": 1754033394353, "duration": 2, "pid": 23380, "index": 33}, {"name": "Require(29) node_modules/egg-view-nunjucks/app/extend/application.js", "start": 1754033394353, "end": 1754033394388, "duration": 35, "pid": 23380, "index": 34}, {"name": "Load extend/request.js", "start": 1754033394392, "end": 1754033394403, "duration": 11, "pid": 23380, "index": 35}, {"name": "Require(30) node_modules/egg/app/extend/request.js", "start": 1754033394397, "end": 1754033394399, "duration": 2, "pid": 23380, "index": 36}, {"name": "Load extend/response.js", "start": 1754033394403, "end": 1754033394413, "duration": 10, "pid": 23380, "index": 37}, {"name": "Require(31) node_modules/egg/app/extend/response.js", "start": 1754033394406, "end": 1754033394409, "duration": 3, "pid": 23380, "index": 38}, {"name": "Load extend/context.js", "start": 1754033394413, "end": 1754033394464, "duration": 51, "pid": 23380, "index": 39}, {"name": "Require(32) node_modules/egg-security/app/extend/context.js", "start": 1754033394413, "end": 1754033394429, "duration": 16, "pid": 23380, "index": 40}, {"name": "Require(33) node_modules/egg-jsonp/app/extend/context.js", "start": 1754033394429, "end": 1754033394431, "duration": 2, "pid": 23380, "index": 41}, {"name": "Require(34) node_modules/egg-i18n/app/extend/context.js", "start": 1754033394432, "end": 1754033394432, "duration": 0, "pid": 23380, "index": 42}, {"name": "Require(35) node_modules/egg-multipart/app/extend/context.js", "start": 1754033394433, "end": 1754033394455, "duration": 22, "pid": 23380, "index": 43}, {"name": "Require(36) node_modules/egg-view/app/extend/context.js", "start": 1754033394456, "end": 1754033394458, "duration": 2, "pid": 23380, "index": 44}, {"name": "Require(37) node_modules/egg/app/extend/context.js", "start": 1754033394459, "end": 1754033394461, "duration": 2, "pid": 23380, "index": 45}, {"name": "Load extend/helper.js", "start": 1754033394465, "end": 1754033394494, "duration": 29, "pid": 23380, "index": 46}, {"name": "Require(38) node_modules/egg-security/app/extend/helper.js", "start": 1754033394465, "end": 1754033394487, "duration": 22, "pid": 23380, "index": 47}, {"name": "Require(39) node_modules/egg/app/extend/helper.js", "start": 1754033394490, "end": 1754033394490, "duration": 0, "pid": 23380, "index": 48}, {"name": "Load app.js", "start": 1754033394494, "end": 1754033394544, "duration": 50, "pid": 23380, "index": 49}, {"name": "Require(40) node_modules/egg-session/app.js", "start": 1754033394494, "end": 1754033394495, "duration": 1, "pid": 23380, "index": 50}, {"name": "Require(41) node_modules/egg-security/app.js", "start": 1754033394495, "end": 1754033394497, "duration": 2, "pid": 23380, "index": 51}, {"name": "Require(42) node_modules/egg-onerror/app.js", "start": 1754033394498, "end": 1754033394510, "duration": 12, "pid": 23380, "index": 52}, {"name": "Require(43) node_modules/egg-i18n/app.js", "start": 1754033394511, "end": 1754033394529, "duration": 18, "pid": 23380, "index": 53}, {"name": "Require(44) node_modules/egg-watcher/app.js", "start": 1754033394529, "end": 1754033394534, "duration": 5, "pid": 23380, "index": 54}, {"name": "Require(45) node_modules/egg-schedule/app.js", "start": 1754033394534, "end": 1754033394535, "duration": 1, "pid": 23380, "index": 55}, {"name": "Require(46) node_modules/egg-multipart/app.js", "start": 1754033394536, "end": 1754033394537, "duration": 1, "pid": 23380, "index": 56}, {"name": "Require(47) node_modules/egg-development/app.js", "start": 1754033394538, "end": 1754033394538, "duration": 0, "pid": 23380, "index": 57}, {"name": "Require(48) node_modules/egg-logrotator/app.js", "start": 1754033394539, "end": 1754033394539, "duration": 0, "pid": 23380, "index": 58}, {"name": "Require(49) node_modules/egg-static/app.js", "start": 1754033394539, "end": 1754033394540, "duration": 1, "pid": 23380, "index": 59}, {"name": "Require(50) node_modules/egg-sequelize/app.js", "start": 1754033394541, "end": 1754033394541, "duration": 0, "pid": 23380, "index": 60}, {"name": "Require(51) node_modules/egg-cors/app.js", "start": 1754033394541, "end": 1754033394542, "duration": 1, "pid": 23380, "index": 61}, {"name": "Require(52) node_modules/egg-view-nunjucks/app.js", "start": 1754033394542, "end": 1754033394543, "duration": 1, "pid": 23380, "index": 62}, {"name": "Require(53) app.js", "start": 1754033394543, "end": 1754033394544, "duration": 1, "pid": 23380, "index": 63}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1754033394555, "end": 1754033395254, "duration": 699, "pid": 23380, "index": 64}, {"name": "Load \"Symbol(model)\" to Application", "start": 1754033395035, "end": 1754033395049, "duration": 14, "pid": 23380, "index": 65}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1754033395049, "end": 1754033395265, "duration": 216, "pid": 23380, "index": 66}, {"name": "Did Load in app.js:didLoad", "start": 1754033395051, "end": 1754033395222, "duration": 171, "pid": 23380, "index": 67}, {"name": "Load Service", "start": 1754033395051, "end": 1754033395059, "duration": 8, "pid": 23380, "index": 68}, {"name": "Load \"service\" to Context", "start": 1754033395051, "end": 1754033395059, "duration": 8, "pid": 23380, "index": 69}, {"name": "Load Middleware", "start": 1754033395060, "end": 1754033395196, "duration": 136, "pid": 23380, "index": 70}, {"name": "Load \"middlewares\" to Application", "start": 1754033395060, "end": 1754033395183, "duration": 123, "pid": 23380, "index": 71}, {"name": "Load Controller", "start": 1754033395196, "end": 1754033395202, "duration": 6, "pid": 23380, "index": 72}, {"name": "Load \"controller\" to Application", "start": 1754033395196, "end": 1754033395202, "duration": 6, "pid": 23380, "index": 73}, {"name": "Load Router", "start": 1754033395202, "end": 1754033395205, "duration": 3, "pid": 23380, "index": 74}, {"name": "Require(54) app/router.js", "start": 1754033395203, "end": 1754033395203, "duration": 0, "pid": 23380, "index": 75}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1754033395203, "end": 1754033395222, "duration": 19, "pid": 23380, "index": 76}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1754033395265, "end": 1754033395265, "duration": 0, "pid": 23380, "index": 77}]