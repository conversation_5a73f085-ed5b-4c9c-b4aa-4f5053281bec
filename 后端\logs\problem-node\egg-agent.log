2025-07-30 11:49:24,751 WARN 25980 Sequelize Error: Unknown database 'problem', sleep 1 seconds to retry...
2025-07-30 11:49:24,168 INFO 25980 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 11:49:24,660 INFO 25980 [egg:core] dump config after load, 6ms
2025-07-30 11:49:24,720 INFO 25980 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 11:49:24,720 INFO 25980 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 11:49:24,721 INFO 25980 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 11:49:24,721 INFO 25980 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 11:49:24,721 INFO 25980 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 11:49:24,721 INFO 25980 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 11:49:24,723 INFO 25980 [egg-watcher:agent] watcher start success
2025-07-30 11:49:25,756 WARN 25980 Sequelize Error: Unknown database 'problem', sleep 1 seconds to retry...
2025-07-30 11:49:26,765 WARN 25980 Sequelize Error: Unknown database 'problem', sleep 1 seconds to retry...
2025-07-30 11:49:38,372 WARN 25772 Sequelize Error: Unknown database 'problem', sleep 1 seconds to retry...
2025-07-30 11:49:37,726 INFO 25772 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 11:49:38,266 INFO 25772 [egg:core] dump config after load, 6ms
2025-07-30 11:49:38,333 INFO 25772 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 11:49:38,333 INFO 25772 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 11:49:38,334 INFO 25772 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 11:49:38,334 INFO 25772 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 11:49:38,334 INFO 25772 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 11:49:38,334 INFO 25772 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 11:49:38,335 INFO 25772 [egg-watcher:agent] watcher start success
2025-07-30 11:49:39,386 WARN 25772 Sequelize Error: Unknown database 'problem', sleep 1 seconds to retry...
2025-07-30 11:49:40,390 WARN 25772 Sequelize Error: Unknown database 'problem', sleep 1 seconds to retry...
2025-07-30 11:49:47,636 WARN 34192 Sequelize Error: Unknown database 'problem', sleep 1 seconds to retry...
2025-07-30 11:49:47,021 INFO 34192 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 11:49:47,528 INFO 34192 [egg:core] dump config after load, 6ms
2025-07-30 11:49:47,603 INFO 34192 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 11:49:47,603 INFO 34192 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 11:49:47,604 INFO 34192 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 11:49:47,604 INFO 34192 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 11:49:47,604 INFO 34192 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 11:49:47,604 INFO 34192 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 11:49:47,606 INFO 34192 [egg-watcher:agent] watcher start success
2025-07-30 11:49:48,647 WARN 34192 Sequelize Error: Unknown database 'problem', sleep 1 seconds to retry...
2025-07-30 11:49:49,660 WARN 34192 Sequelize Error: Unknown database 'problem', sleep 1 seconds to retry...
2025-07-30 11:51:03,010 INFO 31080 [egg-sequelize](4ms) Executed (default): SELECT 1+1 AS result
2025-07-30 11:51:02,443 INFO 31080 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 11:51:02,898 INFO 31080 [egg:core] dump config after load, 5ms
2025-07-30 11:51:02,956 INFO 31080 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 11:51:02,956 INFO 31080 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 11:51:02,956 INFO 31080 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 11:51:02,956 INFO 31080 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 11:51:02,957 INFO 31080 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 11:51:02,957 INFO 31080 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 11:51:02,958 INFO 31080 [egg-watcher:agent] watcher start success
2025-07-30 11:51:03,018 INFO 31080 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [1124ms] - #0 Process Start
                            ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [815ms] - #1 Application Start
                             ▇ [27ms] - #2 Load Plugin
                              ▇ [32ms] - #3 Load Config
                              ▇ [1ms] - #4 Require(0) config/config.default.js
                              ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                              ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                              ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                              ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                              ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                              ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                              ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                              ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                              ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                              ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                              ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                              ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                              ▇ [1ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                              ▇ [1ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                              ▇ [1ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                              ▇ [1ms] - #20 Require(16) node_modules/egg/config/config.default.js
                              ▇ [0ms] - #21 Require(17) config/config.default.js
                              ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                              ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                              ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                              ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                              ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                              ▇ [61ms] - #27 Load extend/agent.js
                              ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                               ▇ [49ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                                ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                                ▇ [58ms] - #31 Load extend/context.js
                                ▇ [15ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                                ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                                 ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                                 ▇ [24ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                                 ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                                 ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                                  ▇ [43ms] - #38 Load agent.js
                                  ▇ [0ms] - #39 Require(32) node_modules/egg-security/agent.js
                                  ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                  ▇ [5ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                  ▇ [2ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                  ▇ [28ms] - #43 Require(36) node_modules/egg-development/agent.js
                                   ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                   ▇ [1ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                   ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                   ▇▇▇▇▇▇▇▇▇▇▇▇▇ [513ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                   ▇▇▇▇▇▇▇▇▇▇▇▇ [484ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                   ▇▇▇▇▇▇▇▇▇▇▇▇▇ [515ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                              ▇ [19ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [118ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 11:51:03,018 INFO 31080 [egg:core] dump config after ready, 6ms
2025-07-30 11:51:50,945 INFO 19772 [egg-sequelize](3ms) Executed (default): SELECT 1+1 AS result
2025-07-30 11:51:50,369 INFO 19772 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 11:51:50,838 INFO 19772 [egg:core] dump config after load, 8ms
2025-07-30 11:51:50,895 INFO 19772 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 11:51:50,895 INFO 19772 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 11:51:50,895 INFO 19772 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 11:51:50,896 INFO 19772 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 11:51:50,896 INFO 19772 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 11:51:50,896 INFO 19772 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 11:51:50,897 INFO 19772 [egg-watcher:agent] watcher start success
2025-07-30 11:51:50,971 INFO 19772 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [945ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [858ms] - #1 Application Start
                          ▇ [33ms] - #2 Load Plugin
                           ▇ [37ms] - #3 Load Config
                           ▇ [1ms] - #4 Require(0) config/config.default.js
                           ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                           ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                           ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                           ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [1ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [64ms] - #27 Load extend/agent.js
                            ▇ [3ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [50ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [58ms] - #31 Load extend/context.js
                              ▇ [18ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                               ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                               ▇ [23ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [3ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                                ▇ [44ms] - #38 Load agent.js
                                ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                                ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                ▇ [6ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [2ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [28ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [1ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [525ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇ [498ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [593ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [25ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [116ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 11:51:50,971 INFO 19772 [egg:core] dump config after ready, 5ms
2025-07-30 11:53:36,639 INFO 30124 [egg-sequelize](4ms) Executed (default): SELECT 1+1 AS result
2025-07-30 11:53:36,058 INFO 30124 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 11:53:36,529 INFO 30124 [egg:core] dump config after load, 6ms
2025-07-30 11:53:36,588 INFO 30124 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 11:53:36,588 INFO 30124 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 11:53:36,588 INFO 30124 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 11:53:36,588 INFO 30124 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 11:53:36,589 INFO 30124 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 11:53:36,589 INFO 30124 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 11:53:36,590 INFO 30124 [egg-watcher:agent] watcher start success
2025-07-30 11:53:36,654 INFO 30124 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [943ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [842ms] - #1 Application Start
                          ▇ [26ms] - #2 Load Plugin
                           ▇ [32ms] - #3 Load Config
                           ▇ [1ms] - #4 Require(0) config/config.default.js
                           ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                           ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                           ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                           ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                           ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                           ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [1ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [1ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [1ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [63ms] - #27 Load extend/agent.js
                            ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [50ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [59ms] - #31 Load extend/context.js
                              ▇ [16ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                              ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                              ▇ [24ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [47ms] - #38 Load agent.js
                               ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                                ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                ▇ [7ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [3ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [30ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [1ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [530ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇ [501ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [586ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [26ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [118ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 11:53:36,654 INFO 30124 [egg:core] dump config after ready, 6ms
2025-07-30 11:54:31,550 INFO 22492 [egg-sequelize](3ms) Executed (default): SELECT 1+1 AS result
2025-07-30 11:54:30,970 INFO 22492 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 11:54:31,439 INFO 22492 [egg:core] dump config after load, 6ms
2025-07-30 11:54:31,497 INFO 22492 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 11:54:31,498 INFO 22492 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 11:54:31,498 INFO 22492 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 11:54:31,498 INFO 22492 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 11:54:31,499 INFO 22492 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 11:54:31,499 INFO 22492 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 11:54:31,500 INFO 22492 [egg-watcher:agent] watcher start success
2025-07-30 11:54:31,559 INFO 22492 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [979ms] - #0 Process Start
                           ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [821ms] - #1 Application Start
                           ▇ [24ms] - #2 Load Plugin
                            ▇ [32ms] - #3 Load Config
                            ▇ [0ms] - #4 Require(0) config/config.default.js
                            ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                            ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                            ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                            ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                            ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                            ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                            ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                             ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                             ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                             ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                             ▇ [1ms] - #26 Require(22) node_modules/egg/config/config.local.js
                             ▇ [61ms] - #27 Load extend/agent.js
                             ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                             ▇ [48ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [54ms] - #31 Load extend/context.js
                              ▇ [16ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                               ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                               ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                               ▇ [23ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                                ▇ [1ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                                ▇ [3ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                                ▇ [42ms] - #38 Load agent.js
                                ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                                ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                ▇ [4ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [2ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [29ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [1ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [528ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇ [497ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [573ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [20ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [119ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 11:54:31,559 INFO 22492 [egg:core] dump config after ready, 6ms
2025-07-30 11:54:54,122 INFO 33000 [egg-sequelize](4ms) Executed (default): SELECT 1+1 AS result
2025-07-30 11:54:53,535 INFO 33000 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 11:54:54,008 INFO 33000 [egg:core] dump config after load, 6ms
2025-07-30 11:54:54,066 INFO 33000 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 11:54:54,066 INFO 33000 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 11:54:54,067 INFO 33000 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 11:54:54,067 INFO 33000 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 11:54:54,067 INFO 33000 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 11:54:54,067 INFO 33000 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 11:54:54,069 INFO 33000 [egg-watcher:agent] watcher start success
2025-07-30 11:54:54,131 INFO 33000 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [962ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [836ms] - #1 Application Start
                           ▇ [25ms] - #2 Load Plugin
                           ▇ [35ms] - #3 Load Config
                            ▇ [1ms] - #4 Require(0) config/config.default.js
                            ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                            ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                            ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                            ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                            ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                            ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                            ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [1ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [1ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [1ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [61ms] - #27 Load extend/agent.js
                             ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                             ▇ [49ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [54ms] - #31 Load extend/context.js
                              ▇ [15ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                               ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                               ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                               ▇ [22ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                                ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                                ▇ [44ms] - #38 Load agent.js
                                ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                                ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                ▇ [5ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [3ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [29ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [1ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [531ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇ [501ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [578ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [20ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [123ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 11:54:54,132 INFO 33000 [egg:core] dump config after ready, 7ms
2025-07-30 11:55:33,446 INFO 28640 [egg-sequelize](3ms) Executed (default): SELECT 1+1 AS result
2025-07-30 11:55:32,850 INFO 28640 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 11:55:33,329 INFO 28640 [egg:core] dump config after load, 6ms
2025-07-30 11:55:33,394 INFO 28640 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 11:55:33,394 INFO 28640 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 11:55:33,394 INFO 28640 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 11:55:33,395 INFO 28640 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 11:55:33,395 INFO 28640 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 11:55:33,395 INFO 28640 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 11:55:33,396 INFO 28640 [egg-watcher:agent] watcher start success
2025-07-30 11:55:33,453 INFO 28640 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [960ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [851ms] - #1 Application Start
                           ▇ [26ms] - #2 Load Plugin
                           ▇ [35ms] - #3 Load Config
                           ▇ [1ms] - #4 Require(0) config/config.default.js
                           ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                            ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                            ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                            ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [1ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [64ms] - #27 Load extend/agent.js
                            ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [50ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [55ms] - #31 Load extend/context.js
                              ▇ [16ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [2ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                               ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                               ▇ [23ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [48ms] - #38 Load agent.js
                                ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                                ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                ▇ [6ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [3ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [29ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [1ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [1ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [543ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [510ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [587ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [20ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [124ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 11:55:33,454 INFO 28640 [egg:core] dump config after ready, 6ms
2025-07-30 15:17:58,957 INFO 30708 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:17:58,318 INFO 30708 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 15:17:58,846 INFO 30708 [egg:core] dump config after load, 6ms
2025-07-30 15:17:58,904 INFO 30708 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 15:17:58,904 INFO 30708 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 15:17:58,905 INFO 30708 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 15:17:58,905 INFO 30708 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 15:17:58,905 INFO 30708 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 15:17:58,906 INFO 30708 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 15:17:58,907 INFO 30708 [egg-watcher:agent] watcher start success
2025-07-30 15:17:58,972 INFO 30708 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [962ms] - #0 Process Start
                         ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [893ms] - #1 Application Start
                          ▇ [25ms] - #2 Load Plugin
                           ▇ [31ms] - #3 Load Config
                           ▇ [1ms] - #4 Require(0) config/config.default.js
                           ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                           ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                           ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                           ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                           ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                           ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                           ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                           ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                           ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                           ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                           ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                           ▇ [1ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                           ▇ [1ms] - #20 Require(16) node_modules/egg/config/config.default.js
                           ▇ [0ms] - #21 Require(17) config/config.default.js
                           ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                           ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                           ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                           ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                           ▇ [1ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [60ms] - #27 Load extend/agent.js
                            ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [47ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                             ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                             ▇ [55ms] - #31 Load extend/context.js
                             ▇ [15ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                              ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                              ▇ [23ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                              ▇ [1ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                              ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [47ms] - #38 Load agent.js
                               ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                               ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                               ▇ [5ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                               ▇ [2ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                               ▇ [32ms] - #43 Require(36) node_modules/egg-development/agent.js
                                ▇ [1ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [586ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [558ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [642ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [20ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [119ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 15:17:58,972 INFO 30708 [egg:core] dump config after ready, 7ms
2025-07-30 15:18:56,673 INFO 37016 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:18:56,022 INFO 37016 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 15:18:56,559 INFO 37016 [egg:core] dump config after load, 6ms
2025-07-30 15:18:56,615 INFO 37016 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 15:18:56,616 INFO 37016 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 15:18:56,616 INFO 37016 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 15:18:56,616 INFO 37016 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 15:18:56,617 INFO 37016 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 15:18:56,617 INFO 37016 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 15:18:56,618 INFO 37016 [egg-watcher:agent] watcher start success
2025-07-30 15:18:56,685 INFO 37016 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [992ms] - #0 Process Start
                         ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [913ms] - #1 Application Start
                          ▇ [25ms] - #2 Load Plugin
                           ▇ [32ms] - #3 Load Config
                           ▇ [0ms] - #4 Require(0) config/config.default.js
                           ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                           ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                           ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                           ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                           ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                           ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                           ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                           ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                           ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                           ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                           ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                           ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                           ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                           ▇ [0ms] - #21 Require(17) config/config.default.js
                           ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                           ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                           ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                           ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                           ▇ [1ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [71ms] - #27 Load extend/agent.js
                            ▇ [3ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [57ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                             ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                             ▇ [56ms] - #31 Load extend/context.js
                             ▇ [16ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                              ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                              ▇ [24ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [45ms] - #38 Load agent.js
                               ▇ [0ms] - #39 Require(32) node_modules/egg-security/agent.js
                               ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                               ▇ [5ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                               ▇ [3ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                               ▇ [29ms] - #43 Require(36) node_modules/egg-development/agent.js
                                ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                ▇ [1ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [594ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [568ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [644ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                              ▇ [20ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [123ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 15:18:56,685 INFO 37016 [egg:core] dump config after ready, 7ms
2025-07-30 15:20:08,000 INFO 4308 [egg-sequelize](3ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:20:07,314 INFO 4308 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 15:20:07,885 INFO 4308 [egg:core] dump config after load, 6ms
2025-07-30 15:20:07,946 INFO 4308 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 15:20:07,946 INFO 4308 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 15:20:07,946 INFO 4308 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 15:20:07,947 INFO 4308 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 15:20:07,947 INFO 4308 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 15:20:07,947 INFO 4308 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 15:20:07,948 INFO 4308 [egg-watcher:agent] watcher start success
2025-07-30 15:20:08,009 INFO 4308 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [1079ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [945ms] - #1 Application Start
                           ▇ [29ms] - #2 Load Plugin
                           ▇ [32ms] - #3 Load Config
                           ▇ [0ms] - #4 Require(0) config/config.default.js
                           ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                            ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                            ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                            ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [1ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [65ms] - #27 Load extend/agent.js
                            ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [53ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [58ms] - #31 Load extend/context.js
                              ▇ [16ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                              ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                              ▇ [24ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [3ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [47ms] - #38 Load agent.js
                               ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                               ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                               ▇ [5ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                               ▇ [3ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                               ▇ [32ms] - #43 Require(36) node_modules/egg-development/agent.js
                                ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                ▇ [1ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [632ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [605ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [670ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                              ▇ [21ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [124ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 15:20:08,010 INFO 4308 [egg:core] dump config after ready, 7ms
2025-07-30 15:22:03,384 INFO 34744 [egg-sequelize](5ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:22:02,701 INFO 34744 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 15:22:03,257 INFO 34744 [egg:core] dump config after load, 5ms
2025-07-30 15:22:03,321 INFO 34744 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 15:22:03,321 INFO 34744 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 15:22:03,322 INFO 34744 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 15:22:03,322 INFO 34744 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 15:22:03,322 INFO 34744 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 15:22:03,323 INFO 34744 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 15:22:03,324 INFO 34744 [egg-watcher:agent] watcher start success
2025-07-30 15:22:03,394 INFO 34744 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [971ms] - #0 Process Start
                         ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [943ms] - #1 Application Start
                         ▇ [27ms] - #2 Load Plugin
                          ▇ [33ms] - #3 Load Config
                          ▇ [1ms] - #4 Require(0) config/config.default.js
                          ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                          ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                          ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                          ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                          ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                          ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                          ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                          ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                          ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                          ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                          ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                          ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                          ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                          ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                           ▇ [1ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                           ▇ [1ms] - #20 Require(16) node_modules/egg/config/config.default.js
                           ▇ [0ms] - #21 Require(17) config/config.default.js
                           ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                           ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                           ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                           ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                           ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                           ▇ [65ms] - #27 Load extend/agent.js
                           ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                           ▇ [51ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                            ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                             ▇ [59ms] - #31 Load extend/context.js
                             ▇ [16ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                             ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                             ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                             ▇ [26ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                              ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                              ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                              ▇ [50ms] - #38 Load agent.js
                              ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                              ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                              ▇ [5ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                              ▇ [3ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                              ▇ [33ms] - #43 Require(36) node_modules/egg-development/agent.js
                               ▇ [1ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                               ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                               ▇ [1ms] - #46 Require(39) node_modules/egg/agent.js
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [620ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [589ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [672ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [20ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [136ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 15:22:03,394 INFO 34744 [egg:core] dump config after ready, 7ms
2025-07-30 15:24:09,517 INFO 5656 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:24:08,882 INFO 5656 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 15:24:09,409 INFO 5656 [egg:core] dump config after load, 6ms
2025-07-30 15:24:09,466 INFO 5656 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 15:24:09,466 INFO 5656 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 15:24:09,467 INFO 5656 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 15:24:09,467 INFO 5656 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 15:24:09,467 INFO 5656 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 15:24:09,467 INFO 5656 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 15:24:09,469 INFO 5656 [egg-watcher:agent] watcher start success
2025-07-30 15:24:09,592 INFO 5656 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [998ms] - #0 Process Start
                         ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [949ms] - #1 Application Start
                          ▇ [26ms] - #2 Load Plugin
                          ▇ [34ms] - #3 Load Config
                          ▇ [0ms] - #4 Require(0) config/config.default.js
                          ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                          ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                          ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                          ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                          ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                          ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                          ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                           ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                           ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                           ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                           ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                           ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                           ▇ [1ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                           ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                           ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                           ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                           ▇ [0ms] - #21 Require(17) config/config.default.js
                           ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                           ▇ [2ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                           ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                           ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                           ▇ [1ms] - #26 Require(22) node_modules/egg/config/config.local.js
                           ▇ [59ms] - #27 Load extend/agent.js
                           ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                           ▇ [46ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                            ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                             ▇ [57ms] - #31 Load extend/context.js
                             ▇ [16ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                             ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                             ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                             ▇ [24ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                              ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                              ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                              ▇ [47ms] - #38 Load agent.js
                              ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                              ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                              ▇ [5ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                              ▇ [3ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                              ▇ [31ms] - #43 Require(36) node_modules/egg-development/agent.js
                               ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                               ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                               ▇ [1ms] - #46 Require(39) node_modules/egg/agent.js
                               ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [584ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                               ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [557ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                               ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [699ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                            ▇ [20ms] - #50 Load "Symbol(model)" to Application
                                             ▇▇ [116ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 15:24:09,592 INFO 5656 [egg:core] dump config after ready, 6ms
2025-07-30 15:24:11,891 WARN 5656 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 15:24:32,217 INFO 33404 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:24:31,548 INFO 33404 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 15:24:32,088 INFO 33404 [egg:core] dump config after load, 6ms
2025-07-30 15:24:32,151 INFO 33404 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 15:24:32,151 INFO 33404 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 15:24:32,152 INFO 33404 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 15:24:32,152 INFO 33404 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 15:24:32,152 INFO 33404 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 15:24:32,152 INFO 33404 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 15:24:32,154 INFO 33404 [egg-watcher:agent] watcher start success
2025-07-30 15:24:32,239 INFO 33404 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [969ms] - #0 Process Start
                         ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [932ms] - #1 Application Start
                         ▇ [26ms] - #2 Load Plugin
                          ▇ [32ms] - #3 Load Config
                          ▇ [1ms] - #4 Require(0) config/config.default.js
                          ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                          ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                          ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                          ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                          ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                          ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                          ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                          ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                          ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                          ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                          ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                          ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                          ▇ [1ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                           ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                           ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                           ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                           ▇ [0ms] - #21 Require(17) config/config.default.js
                           ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                           ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                           ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                           ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                           ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                           ▇ [61ms] - #27 Load extend/agent.js
                           ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                           ▇ [49ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                            ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                            ▇ [59ms] - #31 Load extend/context.js
                             ▇ [16ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                             ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                             ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                             ▇ [23ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                              ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                              ▇ [3ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                              ▇ [51ms] - #38 Load agent.js
                              ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                              ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                              ▇ [7ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                              ▇ [3ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                              ▇ [33ms] - #43 Require(36) node_modules/egg-development/agent.js
                               ▇ [1ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                               ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                               ▇ [1ms] - #46 Require(39) node_modules/egg/agent.js
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [603ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [571ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [660ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [21ms] - #50 Load "Symbol(model)" to Application
                                             ▇▇▇ [137ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 15:24:32,240 INFO 33404 [egg:core] dump config after ready, 15ms
2025-07-30 15:27:45,993 WARN 33404 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\config\config.default.js change
2025-07-30 15:27:45,776 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1753860465768.344,"mtimeMs":1753860465768.344,"ctimeMs":1753860465768.344,"birthtimeMs":1753845075904.9722,"atime":"2025-07-30T07:27:45.768Z","mtime":"2025-07-30T07:27:45.768Z","ctime":"2025-07-30T07:27:45.768Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 15:27:45,780 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1753860465768.344,"mtimeMs":1753860465768.344,"ctimeMs":1753860465768.344,"birthtimeMs":1753845075904.9722,"atime":"2025-07-30T07:27:45.768Z","mtime":"2025-07-30T07:27:45.768Z","ctime":"2025-07-30T07:27:45.768Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 15:27:45,781 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1753860465768.344,"mtimeMs":1753860465768.344,"ctimeMs":1753860465768.344,"birthtimeMs":1753845075904.9722,"atime":"2025-07-30T07:27:45.768Z","mtime":"2025-07-30T07:27:45.768Z","ctime":"2025-07-30T07:27:45.768Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 15:27:48,274 WARN 33404 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 15:27:53,972 WARN 33404 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\config\config.default.js change
2025-07-30 15:27:53,771 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1753860473756.1506,"mtimeMs":1753860473756.1506,"ctimeMs":1753860473756.1506,"birthtimeMs":1753845075904.9722,"atime":"2025-07-30T07:27:53.756Z","mtime":"2025-07-30T07:27:53.756Z","ctime":"2025-07-30T07:27:53.756Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 15:27:53,771 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1753860473756.1506,"mtimeMs":1753860473756.1506,"ctimeMs":1753860473756.1506,"birthtimeMs":1753845075904.9722,"atime":"2025-07-30T07:27:53.756Z","mtime":"2025-07-30T07:27:53.756Z","ctime":"2025-07-30T07:27:53.756Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 15:27:53,772 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1753860473756.1506,"mtimeMs":1753860473756.1506,"ctimeMs":1753860473756.1506,"birthtimeMs":1753845075904.9722,"atime":"2025-07-30T07:27:53.756Z","mtime":"2025-07-30T07:27:53.756Z","ctime":"2025-07-30T07:27:53.756Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 15:27:56,272 WARN 33404 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 15:29:34,437 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":837,"blocks":8,"atimeMs":1753860574427.8352,"mtimeMs":1753860574427.8352,"ctimeMs":1753860574427.8352,"birthtimeMs":1753845075890.531,"atime":"2025-07-30T07:29:34.428Z","mtime":"2025-07-30T07:29:34.428Z","ctime":"2025-07-30T07:29:34.428Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 15:29:34,438 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":837,"blocks":8,"atimeMs":1753860574427.8352,"mtimeMs":1753860574427.8352,"ctimeMs":1753860574427.8352,"birthtimeMs":1753845075890.531,"atime":"2025-07-30T07:29:34.428Z","mtime":"2025-07-30T07:29:34.428Z","ctime":"2025-07-30T07:29:34.428Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 15:29:34,439 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":837,"blocks":8,"atimeMs":1753860574427.8352,"mtimeMs":1753860574427.8352,"ctimeMs":1753860574427.8352,"birthtimeMs":1753845075890.531,"atime":"2025-07-30T07:29:34.428Z","mtime":"2025-07-30T07:29:34.428Z","ctime":"2025-07-30T07:29:34.428Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 15:29:34,640 WARN 33404 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\router.js change
2025-07-30 15:29:36,873 WARN 33404 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 15:31:28,091 WARN 33404 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\utils\tool.js change
2025-07-30 15:31:27,885 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils\\tool.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":14636698789334984,"size":2765,"blocks":8,"atimeMs":1753860687880.4595,"mtimeMs":1732008650000,"ctimeMs":1753845075901.432,"birthtimeMs":1753845075900.4263,"atime":"2025-07-30T07:31:27.880Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.901Z","birthtime":"2025-07-30T03:11:15.900Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 15:31:30,627 WARN 33404 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:06:16,002 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753862775999.0613,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-30T08:06:15.999Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 16:06:23,998 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753862783986.0315,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-30T08:06:23.986Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 16:06:23,998 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1753862783986.0315,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-07-30T08:06:23.986Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 16:06:23,998 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753862783986.0315,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-30T08:06:23.986Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 16:06:23,999 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1753862783987.0312,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-07-30T08:06:23.987Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 16:06:23,999 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753862783987.0312,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-30T08:06:23.987Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 16:16:15,279 WARN 33404 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app.js change
2025-07-30 16:16:14,951 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2813,"blocks":8,"atimeMs":1753863374948.7144,"mtimeMs":1753847118677.6086,"ctimeMs":1753847118677.6086,"birthtimeMs":1753847118676.5964,"atime":"2025-07-30T08:16:14.949Z","mtime":"2025-07-30T03:45:18.678Z","ctime":"2025-07-30T03:45:18.678Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:16:15,068 INFO 33404 [egg-watcher] Received a change event from eventSource: {"path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js","event":"change","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":18014398509862772,"size":595,"blocks":1,"atimeMs":1753863375066.4006,"mtimeMs":1753845216497.8264,"ctimeMs":1753845216497.8264,"birthtimeMs":1753845075856.6013,"atime":"2025-07-30T08:16:15.066Z","mtime":"2025-07-30T03:13:36.498Z","ctime":"2025-07-30T03:13:36.498Z","birthtime":"2025-07-30T03:11:15.857Z"}}
2025-07-30 16:16:16,151 WARN 33404 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:16:15,941 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":2772,"blocks":8,"atimeMs":1753863375936.2944,"mtimeMs":1753847102917.4177,"ctimeMs":1753847102917.4177,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:16:15.936Z","mtime":"2025-07-30T03:45:02.917Z","ctime":"2025-07-30T03:45:02.917Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:16:16,684 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\plugin.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700561,"size":389,"blocks":0,"atimeMs":1753863376680.2053,"mtimeMs":1753845180012.2527,"ctimeMs":1753845180012.2527,"birthtimeMs":1753845075908.4712,"atime":"2025-07-30T08:16:16.680Z","mtime":"2025-07-30T03:13:00.012Z","ctime":"2025-07-30T03:13:00.012Z","birthtime":"2025-07-30T03:11:15.908Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:16:16,734 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":2765,"blocks":8,"atimeMs":1753863376718.912,"mtimeMs":1753847132395.9258,"ctimeMs":1753847132395.9258,"birthtimeMs":1753847132395.9258,"atime":"2025-07-30T08:16:16.719Z","mtime":"2025-07-30T03:45:32.396Z","ctime":"2025-07-30T03:45:32.396Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:16:16,735 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":7881299348279197,"size":997,"blocks":8,"atimeMs":1753863376721.2,"mtimeMs":1753848028559.5122,"ctimeMs":1753848028559.5122,"birthtimeMs":1753845075867.3916,"atime":"2025-07-30T08:16:16.721Z","mtime":"2025-07-30T04:00:28.560Z","ctime":"2025-07-30T04:00:28.560Z","birthtime":"2025-07-30T03:11:15.867Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:16:16,736 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule\\sendMessage.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697619396,"size":350,"blocks":0,"atimeMs":1753863376723.849,"mtimeMs":1732008650000,"ctimeMs":1753845075893.994,"birthtimeMs":1753845075892.9944,"atime":"2025-07-30T08:16:16.724Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.894Z","birthtime":"2025-07-30T03:11:15.893Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:16:16,736 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4222124651529413,"size":1035,"blocks":8,"atimeMs":1753863376722.3416,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1753847979734.9976,"atime":"2025-07-30T08:16:16.722Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-07-30T03:59:39.735Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:16:16,736 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861084092,"size":910,"blocks":8,"atimeMs":1753863376722.8457,"mtimeMs":1753848023924.8098,"ctimeMs":1753848023924.8098,"birthtimeMs":1753848013093.8396,"atime":"2025-07-30T08:16:16.723Z","mtime":"2025-07-30T04:00:23.925Z","ctime":"2025-07-30T04:00:23.925Z","birthtime":"2025-07-30T04:00:13.094Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:16:16,944 WARN 33404 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\model\user.js change
2025-07-30 16:16:17,874 WARN 33404 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:16:17,936 WARN 33404 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:18:46,909 WARN 33404 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\location.js change
2025-07-30 16:18:46,705 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":3640,"blocks":8,"atimeMs":1753863526690.2014,"mtimeMs":1753863526690.2014,"ctimeMs":1753863526690.2014,"birthtimeMs":1753847118676.5964,"atime":"2025-07-30T08:18:46.690Z","mtime":"2025-07-30T08:18:46.690Z","ctime":"2025-07-30T08:18:46.690Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:18:46,706 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":3640,"blocks":8,"atimeMs":1753863526690.2014,"mtimeMs":1753863526690.2014,"ctimeMs":1753863526690.2014,"birthtimeMs":1753847118676.5964,"atime":"2025-07-30T08:18:46.690Z","mtime":"2025-07-30T08:18:46.690Z","ctime":"2025-07-30T08:18:46.690Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:18:46,706 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":3640,"blocks":8,"atimeMs":1753863526690.2014,"mtimeMs":1753863526690.2014,"ctimeMs":1753863526690.2014,"birthtimeMs":1753847118676.5964,"atime":"2025-07-30T08:18:46.690Z","mtime":"2025-07-30T08:18:46.690Z","ctime":"2025-07-30T08:18:46.690Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:18:48,977 WARN 33404 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:18:52,890 WARN 33404 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\location.js change
2025-07-30 16:18:52,675 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":3640,"blocks":8,"atimeMs":1753863532659.4622,"mtimeMs":1753863532659.4622,"ctimeMs":1753863532659.4622,"birthtimeMs":1753847118676.5964,"atime":"2025-07-30T08:18:52.659Z","mtime":"2025-07-30T08:18:52.659Z","ctime":"2025-07-30T08:18:52.659Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:18:52,675 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":3640,"blocks":8,"atimeMs":1753863532659.4622,"mtimeMs":1753863532659.4622,"ctimeMs":1753863532659.4622,"birthtimeMs":1753847118676.5964,"atime":"2025-07-30T08:18:52.659Z","mtime":"2025-07-30T08:18:52.659Z","ctime":"2025-07-30T08:18:52.659Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:18:52,676 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":3640,"blocks":8,"atimeMs":1753863532659.4622,"mtimeMs":1753863532659.4622,"ctimeMs":1753863532659.4622,"birthtimeMs":1753847118676.5964,"atime":"2025-07-30T08:18:52.659Z","mtime":"2025-07-30T08:18:52.659Z","ctime":"2025-07-30T08:18:52.659Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:18:55,101 WARN 33404 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:19:03,908 WARN 33404 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\router.js change
2025-07-30 16:19:03,704 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":902,"blocks":8,"atimeMs":1753863543698.767,"mtimeMs":1753863543698.767,"ctimeMs":1753863543698.767,"birthtimeMs":1753845075890.531,"atime":"2025-07-30T08:19:03.699Z","mtime":"2025-07-30T08:19:03.699Z","ctime":"2025-07-30T08:19:03.699Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:19:03,705 INFO 33404 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":902,"blocks":8,"atimeMs":1753863543698.767,"mtimeMs":1753863543698.767,"ctimeMs":1753863543698.767,"birthtimeMs":1753845075890.531,"atime":"2025-07-30T08:19:03.699Z","mtime":"2025-07-30T08:19:03.699Z","ctime":"2025-07-30T08:19:03.699Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:19:06,289 WARN 33404 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:20:32,957 INFO 19928 [egg-sequelize](3ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:20:32,394 INFO 19928 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 16:20:32,850 INFO 19928 [egg:core] dump config after load, 5ms
2025-07-30 16:20:32,906 INFO 19928 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 16:20:32,906 INFO 19928 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 16:20:32,907 INFO 19928 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 16:20:32,907 INFO 19928 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 16:20:32,907 INFO 19928 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 16:20:32,907 INFO 19928 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 16:20:32,908 INFO 19928 [egg-watcher:agent] watcher start success
2025-07-30 16:20:32,977 INFO 19928 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [949ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [814ms] - #1 Application Start
                           ▇ [24ms] - #2 Load Plugin
                            ▇ [30ms] - #3 Load Config
                            ▇ [1ms] - #4 Require(0) config/config.default.js
                            ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                            ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                            ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                            ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                            ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                            ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                            ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [65ms] - #27 Load extend/agent.js
                            ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [54ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [53ms] - #31 Load extend/context.js
                              ▇ [16ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                               ▇ [2ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                               ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                               ▇ [22ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                                ▇ [1ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                                ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                                ▇ [43ms] - #38 Load agent.js
                                ▇ [0ms] - #39 Require(32) node_modules/egg-security/agent.js
                                ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                ▇ [5ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [3ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [28ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [1ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [1ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [511ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇ [483ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [571ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [17ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [114ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 16:20:32,977 INFO 19928 [egg:core] dump config after ready, 7ms
2025-07-30 16:21:14,432 INFO 38496 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:21:13,846 INFO 38496 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-30 16:21:14,319 INFO 38496 [egg:core] dump config after load, 6ms
2025-07-30 16:21:14,382 INFO 38496 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-30 16:21:14,383 INFO 38496 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-30 16:21:14,383 INFO 38496 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-30 16:21:14,383 INFO 38496 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-30 16:21:14,384 INFO 38496 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-30 16:21:14,384 INFO 38496 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-30 16:21:14,385 INFO 38496 [egg-watcher:agent] watcher start success
2025-07-30 16:21:14,443 INFO 38496 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [965ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [835ms] - #1 Application Start
                           ▇ [25ms] - #2 Load Plugin
                           ▇ [29ms] - #3 Load Config
                           ▇ [0ms] - #4 Require(0) config/config.default.js
                            ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                            ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                            ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                            ▇ [0ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                            ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                            ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                            ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [61ms] - #27 Load extend/agent.js
                            ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [48ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [58ms] - #31 Load extend/context.js
                              ▇ [15ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                               ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                               ▇ [25ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                                ▇ [47ms] - #38 Load agent.js
                                ▇ [0ms] - #39 Require(32) node_modules/egg-security/agent.js
                                ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                ▇ [6ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [2ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [28ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [1ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [1ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [1ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [537ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇ [506ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [573ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [19ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [121ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-30 16:21:14,444 INFO 38496 [egg:core] dump config after ready, 7ms
2025-07-30 16:25:04,862 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:25:04,648 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3620,"blocks":8,"atimeMs":1753863904638.061,"mtimeMs":1753863904638.061,"ctimeMs":1753863904638.061,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:25:04.638Z","mtime":"2025-07-30T08:25:04.638Z","ctime":"2025-07-30T08:25:04.638Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:25:04,650 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3620,"blocks":8,"atimeMs":1753863904638.061,"mtimeMs":1753863904638.061,"ctimeMs":1753863904638.061,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:25:04.638Z","mtime":"2025-07-30T08:25:04.638Z","ctime":"2025-07-30T08:25:04.638Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:25:04,650 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3620,"blocks":8,"atimeMs":1753863904638.061,"mtimeMs":1753863904638.061,"ctimeMs":1753863904638.061,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:25:04.638Z","mtime":"2025-07-30T08:25:04.638Z","ctime":"2025-07-30T08:25:04.638Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:25:07,288 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:25:24,290 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:25:24,082 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3621,"blocks":8,"atimeMs":1753863924077.838,"mtimeMs":1753863924077.838,"ctimeMs":1753863924077.838,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:25:24.078Z","mtime":"2025-07-30T08:25:24.078Z","ctime":"2025-07-30T08:25:24.078Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:25:24,083 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3621,"blocks":8,"atimeMs":1753863924077.838,"mtimeMs":1753863924077.838,"ctimeMs":1753863924077.838,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:25:24.078Z","mtime":"2025-07-30T08:25:24.078Z","ctime":"2025-07-30T08:25:24.078Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:25:26,501 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:25:37,234 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:25:37,031 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3654,"blocks":8,"atimeMs":1753863937020.2756,"mtimeMs":1753863937020.2756,"ctimeMs":1753863937020.2756,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:25:37.020Z","mtime":"2025-07-30T08:25:37.020Z","ctime":"2025-07-30T08:25:37.020Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:25:37,032 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3654,"blocks":8,"atimeMs":1753863937020.2756,"mtimeMs":1753863937020.2756,"ctimeMs":1753863937020.2756,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:25:37.020Z","mtime":"2025-07-30T08:25:37.020Z","ctime":"2025-07-30T08:25:37.020Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:25:37,032 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3654,"blocks":8,"atimeMs":1753863937020.2756,"mtimeMs":1753863937020.2756,"ctimeMs":1753863937020.2756,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:25:37.020Z","mtime":"2025-07-30T08:25:37.020Z","ctime":"2025-07-30T08:25:37.020Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:25:39,578 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:25:52,200 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:25:51,986 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3634,"blocks":8,"atimeMs":1753863951980.8474,"mtimeMs":1753863951980.8474,"ctimeMs":1753863951980.8474,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:25:51.981Z","mtime":"2025-07-30T08:25:51.981Z","ctime":"2025-07-30T08:25:51.981Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:25:51,987 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3634,"blocks":8,"atimeMs":1753863951980.8474,"mtimeMs":1753863951980.8474,"ctimeMs":1753863951980.8474,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:25:51.981Z","mtime":"2025-07-30T08:25:51.981Z","ctime":"2025-07-30T08:25:51.981Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:25:54,532 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:26:07,815 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3635,"blocks":8,"atimeMs":1753863967811.4868,"mtimeMs":1753863967811.4868,"ctimeMs":1753863967811.4868,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:26:07.811Z","mtime":"2025-07-30T08:26:07.811Z","ctime":"2025-07-30T08:26:07.811Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:26:07,815 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3635,"blocks":8,"atimeMs":1753863967811.4868,"mtimeMs":1753863967811.4868,"ctimeMs":1753863967811.4868,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:26:07.811Z","mtime":"2025-07-30T08:26:07.811Z","ctime":"2025-07-30T08:26:07.811Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:26:08,022 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:26:10,222 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:26:17,235 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:26:17,030 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3605,"blocks":8,"atimeMs":1753863977018.011,"mtimeMs":1753863977018.011,"ctimeMs":1753863977018.011,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:26:17.018Z","mtime":"2025-07-30T08:26:17.018Z","ctime":"2025-07-30T08:26:17.018Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:26:17,031 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3605,"blocks":8,"atimeMs":1753863977018.011,"mtimeMs":1753863977018.011,"ctimeMs":1753863977018.011,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:26:17.018Z","mtime":"2025-07-30T08:26:17.018Z","ctime":"2025-07-30T08:26:17.018Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:26:19,383 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:26:41,206 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3591,"blocks":8,"atimeMs":1753864001190.4963,"mtimeMs":1753864001190.4963,"ctimeMs":1753864001190.4963,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:26:41.190Z","mtime":"2025-07-30T08:26:41.190Z","ctime":"2025-07-30T08:26:41.190Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:26:41,207 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3591,"blocks":8,"atimeMs":1753864001190.4963,"mtimeMs":1753864001190.4963,"ctimeMs":1753864001190.4963,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:26:41.190Z","mtime":"2025-07-30T08:26:41.190Z","ctime":"2025-07-30T08:26:41.190Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:26:41,207 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3591,"blocks":8,"atimeMs":1753864001190.4963,"mtimeMs":1753864001190.4963,"ctimeMs":1753864001190.4963,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:26:41.190Z","mtime":"2025-07-30T08:26:41.190Z","ctime":"2025-07-30T08:26:41.190Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:26:41,409 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:26:43,662 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:26:51,268 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3605,"blocks":8,"atimeMs":1753864011259.3342,"mtimeMs":1753864011259.3342,"ctimeMs":1753864011259.3342,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:26:51.259Z","mtime":"2025-07-30T08:26:51.259Z","ctime":"2025-07-30T08:26:51.259Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:26:51,269 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3605,"blocks":8,"atimeMs":1753864011259.3342,"mtimeMs":1753864011259.3342,"ctimeMs":1753864011259.3342,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:26:51.259Z","mtime":"2025-07-30T08:26:51.259Z","ctime":"2025-07-30T08:26:51.259Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:26:51,269 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3605,"blocks":8,"atimeMs":1753864011259.3342,"mtimeMs":1753864011259.3342,"ctimeMs":1753864011259.3342,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:26:51.259Z","mtime":"2025-07-30T08:26:51.259Z","ctime":"2025-07-30T08:26:51.259Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:26:51,481 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:26:53,594 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:27:07,038 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:27:06,837 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3620,"blocks":8,"atimeMs":1753864026822.3105,"mtimeMs":1753864026822.3105,"ctimeMs":1753864026822.3105,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:06.822Z","mtime":"2025-07-30T08:27:06.822Z","ctime":"2025-07-30T08:27:06.822Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:06,838 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3620,"blocks":8,"atimeMs":1753864026822.3105,"mtimeMs":1753864026822.3105,"ctimeMs":1753864026822.3105,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:06.822Z","mtime":"2025-07-30T08:27:06.822Z","ctime":"2025-07-30T08:27:06.822Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:06,838 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3620,"blocks":8,"atimeMs":1753864026822.3105,"mtimeMs":1753864026822.3105,"ctimeMs":1753864026822.3105,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:06.822Z","mtime":"2025-07-30T08:27:06.822Z","ctime":"2025-07-30T08:27:06.822Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:09,174 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:27:16,001 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:27:15,793 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3605,"blocks":8,"atimeMs":1753864035785.5957,"mtimeMs":1753864035785.5957,"ctimeMs":1753864035785.5957,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:15.786Z","mtime":"2025-07-30T08:27:15.786Z","ctime":"2025-07-30T08:27:15.786Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:15,793 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3605,"blocks":8,"atimeMs":1753864035785.5957,"mtimeMs":1753864035785.5957,"ctimeMs":1753864035785.5957,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:15.786Z","mtime":"2025-07-30T08:27:15.786Z","ctime":"2025-07-30T08:27:15.786Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:15,794 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3605,"blocks":8,"atimeMs":1753864035785.5957,"mtimeMs":1753864035785.5957,"ctimeMs":1753864035785.5957,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:15.786Z","mtime":"2025-07-30T08:27:15.786Z","ctime":"2025-07-30T08:27:15.786Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:18,069 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:27:47,708 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3617,"blocks":8,"atimeMs":1753864067691.9084,"mtimeMs":1753864067691.9084,"ctimeMs":1753864067691.9084,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:47.692Z","mtime":"2025-07-30T08:27:47.692Z","ctime":"2025-07-30T08:27:47.692Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:47,708 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3617,"blocks":8,"atimeMs":1753864067691.9084,"mtimeMs":1753864067691.9084,"ctimeMs":1753864067691.9084,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:47.692Z","mtime":"2025-07-30T08:27:47.692Z","ctime":"2025-07-30T08:27:47.692Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:47,709 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3617,"blocks":8,"atimeMs":1753864067691.9084,"mtimeMs":1753864067691.9084,"ctimeMs":1753864067691.9084,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:47.692Z","mtime":"2025-07-30T08:27:47.692Z","ctime":"2025-07-30T08:27:47.692Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:47,911 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:27:50,113 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:27:53,675 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:27:53,470 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3590,"blocks":8,"atimeMs":1753864073457.5627,"mtimeMs":1753864073457.5627,"ctimeMs":1753864073457.5627,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:53.458Z","mtime":"2025-07-30T08:27:53.458Z","ctime":"2025-07-30T08:27:53.458Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:53,471 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3590,"blocks":8,"atimeMs":1753864073457.5627,"mtimeMs":1753864073457.5627,"ctimeMs":1753864073457.5627,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:53.458Z","mtime":"2025-07-30T08:27:53.458Z","ctime":"2025-07-30T08:27:53.458Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:53,471 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3590,"blocks":8,"atimeMs":1753864073457.5627,"mtimeMs":1753864073457.5627,"ctimeMs":1753864073457.5627,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:27:53.458Z","mtime":"2025-07-30T08:27:53.458Z","ctime":"2025-07-30T08:27:53.458Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:54,813 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1753864074810.1094,"mtimeMs":1753860473756.1506,"ctimeMs":1753860473756.1506,"birthtimeMs":1753845075904.9722,"atime":"2025-07-30T08:27:54.810Z","mtime":"2025-07-30T07:27:53.756Z","ctime":"2025-07-30T07:27:53.756Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:27:55,819 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:27:55,871 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:27:55,029 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\config\config.default.js change
2025-07-30 16:28:15,087 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-30 16:28:14,871 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3588,"blocks":8,"atimeMs":1753864094855.4304,"mtimeMs":1753864094855.4304,"ctimeMs":1753864094855.4304,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:28:14.855Z","mtime":"2025-07-30T08:28:14.855Z","ctime":"2025-07-30T08:28:14.855Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:28:14,871 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3588,"blocks":8,"atimeMs":1753864094855.4304,"mtimeMs":1753864094855.4304,"ctimeMs":1753864094855.4304,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:28:14.855Z","mtime":"2025-07-30T08:28:14.855Z","ctime":"2025-07-30T08:28:14.855Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:28:14,871 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3588,"blocks":8,"atimeMs":1753864094855.4304,"mtimeMs":1753864094855.4304,"ctimeMs":1753864094855.4304,"birthtimeMs":1753847102917.4177,"atime":"2025-07-30T08:28:14.855Z","mtime":"2025-07-30T08:28:14.855Z","ctime":"2025-07-30T08:28:14.855Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:28:17,300 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:28:24,648 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\location.js change
2025-07-30 16:28:24,437 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2751,"blocks":8,"atimeMs":1753864104432.0076,"mtimeMs":1753864104432.0076,"ctimeMs":1753864104432.0076,"birthtimeMs":1753847118676.5964,"atime":"2025-07-30T08:28:24.432Z","mtime":"2025-07-30T08:28:24.432Z","ctime":"2025-07-30T08:28:24.432Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:28:24,437 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2751,"blocks":8,"atimeMs":1753864104432.0076,"mtimeMs":1753864104432.0076,"ctimeMs":1753864104432.0076,"birthtimeMs":1753847118676.5964,"atime":"2025-07-30T08:28:24.432Z","mtime":"2025-07-30T08:28:24.432Z","ctime":"2025-07-30T08:28:24.432Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:28:26,897 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:28:30,107 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-07-30 16:28:29,892 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":2763,"blocks":8,"atimeMs":1753864109877.0894,"mtimeMs":1753864109877.0894,"ctimeMs":1753864109877.0894,"birthtimeMs":1753847132395.9258,"atime":"2025-07-30T08:28:29.877Z","mtime":"2025-07-30T08:28:29.877Z","ctime":"2025-07-30T08:28:29.877Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:28:29,893 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":2763,"blocks":8,"atimeMs":1753864109877.0894,"mtimeMs":1753864109877.0894,"ctimeMs":1753864109877.0894,"birthtimeMs":1753847132395.9258,"atime":"2025-07-30T08:28:29.877Z","mtime":"2025-07-30T08:28:29.877Z","ctime":"2025-07-30T08:28:29.877Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:28:29,893 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":2763,"blocks":8,"atimeMs":1753864109877.0894,"mtimeMs":1753864109877.0894,"ctimeMs":1753864109877.0894,"birthtimeMs":1753847132395.9258,"atime":"2025-07-30T08:28:29.877Z","mtime":"2025-07-30T08:28:29.877Z","ctime":"2025-07-30T08:28:29.877Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:28:32,366 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:39:57,954 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\router.js change
2025-07-30 16:39:57,750 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":902,"blocks":8,"atimeMs":1753864797744.53,"mtimeMs":1753864797744.53,"ctimeMs":1753864797744.53,"birthtimeMs":1753845075890.531,"atime":"2025-07-30T08:39:57.745Z","mtime":"2025-07-30T08:39:57.745Z","ctime":"2025-07-30T08:39:57.745Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:39:57,750 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":902,"blocks":8,"atimeMs":1753864797744.53,"mtimeMs":1753864797744.53,"ctimeMs":1753864797744.53,"birthtimeMs":1753845075890.531,"atime":"2025-07-30T08:39:57.745Z","mtime":"2025-07-30T08:39:57.745Z","ctime":"2025-07-30T08:39:57.745Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:40:00,413 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 16:49:40,043 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\utils\tool.js change
2025-07-30 16:49:39,836 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils\\tool.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":14636698789334984,"size":2765,"blocks":8,"atimeMs":1753865379832.7737,"mtimeMs":1732008650000,"ctimeMs":1753845075901.432,"birthtimeMs":1753845075900.4263,"atime":"2025-07-30T08:49:39.833Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.901Z","birthtime":"2025-07-30T03:11:15.900Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 16:49:42,305 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-30 17:23:36,937 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753867416920.3926,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-30T09:23:36.920Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 17:23:36,937 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753867416920.3926,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-30T09:23:36.920Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 17:23:36,938 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1753867416920.3926,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-07-30T09:23:36.920Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 17:23:36,938 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753867416921.444,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-30T09:23:36.921Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 17:23:36,939 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1753867416921.444,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-07-30T09:23:36.921Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 17:23:36,939 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753867416921.444,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-30T09:23:36.921Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-30 17:26:22,104 INFO 38496 [egg-watcher] Received a change event from eventSource: {"path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js","event":"change","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":18014398509862772,"size":595,"blocks":1,"atimeMs":1753867582096.0234,"mtimeMs":1753845216497.8264,"ctimeMs":1753845216497.8264,"birthtimeMs":1753845075856.6013,"atime":"2025-07-30T09:26:22.096Z","mtime":"2025-07-30T03:13:36.498Z","ctime":"2025-07-30T03:13:36.498Z","birthtime":"2025-07-30T03:11:15.857Z"}}
2025-07-30 17:26:22,128 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":7881299348279197,"size":997,"blocks":8,"atimeMs":1753867582116.9934,"mtimeMs":1753848028559.5122,"ctimeMs":1753848028559.5122,"birthtimeMs":1753845075867.3916,"atime":"2025-07-30T09:26:22.117Z","mtime":"2025-07-30T04:00:28.560Z","ctime":"2025-07-30T04:00:28.560Z","birthtime":"2025-07-30T03:11:15.867Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 17:26:22,135 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4222124651529413,"size":1035,"blocks":8,"atimeMs":1753867582124.5916,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1753847979734.9976,"atime":"2025-07-30T09:26:22.125Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-07-30T03:59:39.735Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 17:26:22,143 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861084092,"size":910,"blocks":8,"atimeMs":1753867582131.8418,"mtimeMs":1753848023924.8098,"ctimeMs":1753848023924.8098,"birthtimeMs":1753848013093.8396,"atime":"2025-07-30T09:26:22.132Z","mtime":"2025-07-30T04:00:23.925Z","ctime":"2025-07-30T04:00:23.925Z","birthtime":"2025-07-30T04:00:13.094Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 17:26:22,157 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule\\sendMessage.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697619396,"size":350,"blocks":0,"atimeMs":1753867582147.5742,"mtimeMs":1732008650000,"ctimeMs":1753845075893.994,"birthtimeMs":1753845075892.9944,"atime":"2025-07-30T09:26:22.148Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.894Z","birthtime":"2025-07-30T03:11:15.893Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 17:26:22,181 INFO 38496 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\plugin.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700561,"size":389,"blocks":0,"atimeMs":1753867582171.0427,"mtimeMs":1753845180012.2527,"ctimeMs":1753845180012.2527,"birthtimeMs":1753845075908.4712,"atime":"2025-07-30T09:26:22.171Z","mtime":"2025-07-30T03:13:00.012Z","ctime":"2025-07-30T03:13:00.012Z","birthtime":"2025-07-30T03:11:15.908Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-30 17:26:22,396 WARN 38496 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\config\plugin.js change
2025-07-30 17:26:24,763 WARN 38496 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 09:23:57,719 INFO 25392 [egg-sequelize](3ms) Executed (default): SELECT 1+1 AS result
2025-07-31 09:23:57,042 INFO 25392 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-31 09:23:57,593 INFO 25392 [egg:core] dump config after load, 8ms
2025-07-31 09:23:57,661 INFO 25392 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-31 09:23:57,661 INFO 25392 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-31 09:23:57,662 INFO 25392 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-31 09:23:57,662 INFO 25392 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-31 09:23:57,662 INFO 25392 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-31 09:23:57,662 INFO 25392 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-31 09:23:57,663 INFO 25392 [egg-watcher:agent] watcher start success
2025-07-31 09:23:57,748 INFO 25392 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [968ms] - #0 Process Start
                        ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [963ms] - #1 Application Start
                         ▇ [22ms] - #2 Load Plugin
                          ▇ [29ms] - #3 Load Config
                          ▇ [0ms] - #4 Require(0) config/config.default.js
                          ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                          ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                          ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                          ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                          ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                          ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                          ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                          ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                          ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                          ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                          ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                          ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                          ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                          ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                          ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                          ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                          ▇ [0ms] - #21 Require(17) config/config.default.js
                          ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                          ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                          ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                          ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                          ▇ [1ms] - #26 Require(22) node_modules/egg/config/config.local.js
                          ▇ [69ms] - #27 Load extend/agent.js
                          ▇ [3ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                          ▇ [55ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                            ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                            ▇ [69ms] - #31 Load extend/context.js
                            ▇ [21ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                             ▇ [4ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                             ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                             ▇ [30ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                              ▇ [3ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                              ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                              ▇ [52ms] - #38 Load agent.js
                              ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                              ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                              ▇ [6ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                              ▇ [2ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                              ▇ [35ms] - #43 Require(36) node_modules/egg-development/agent.js
                               ▇ [1ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                               ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                               ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                               ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [618ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                               ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [586ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                               ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [695ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [25ms] - #50 Load "Symbol(model)" to Application
                                             ▇▇▇ [137ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-31 09:23:57,749 INFO 25392 [egg:core] dump config after ready, 8ms
2025-07-31 09:23:59,346 WARN 25392 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app.js change
2025-07-31 09:23:59,139 INFO 25392 [egg-watcher] Received a change event from eventSource: {"path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js","event":"change","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":18014398509862772,"size":595,"blocks":1,"atimeMs":1753925039143.5833,"mtimeMs":1753845216497.8264,"ctimeMs":1753845216497.8264,"birthtimeMs":1753845075856.6013,"atime":"2025-07-31T01:23:59.144Z","mtime":"2025-07-30T03:13:36.498Z","ctime":"2025-07-30T03:13:36.498Z","birthtime":"2025-07-30T03:11:15.857Z"}}
2025-07-31 09:23:59,903 INFO 25392 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3588,"blocks":8,"atimeMs":1753925039888.0754,"mtimeMs":1753864094855.4304,"ctimeMs":1753864094855.4304,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T01:23:59.888Z","mtime":"2025-07-30T08:28:14.855Z","ctime":"2025-07-30T08:28:14.855Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 09:23:59,906 INFO 25392 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2751,"blocks":8,"atimeMs":1753925039888.0754,"mtimeMs":1753864104432.0076,"ctimeMs":1753864104432.0076,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T01:23:59.888Z","mtime":"2025-07-30T08:28:24.432Z","ctime":"2025-07-30T08:28:24.432Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 09:23:59,906 INFO 25392 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":2763,"blocks":8,"atimeMs":1753925039903.7024,"mtimeMs":1753864109877.0894,"ctimeMs":1753864109877.0894,"birthtimeMs":1753847132395.9258,"atime":"2025-07-31T01:23:59.904Z","mtime":"2025-07-30T08:28:29.877Z","ctime":"2025-07-30T08:28:29.877Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 09:23:59,906 INFO 25392 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":902,"blocks":8,"atimeMs":1753925039903.7024,"mtimeMs":1753864797744.53,"ctimeMs":1753864797744.53,"birthtimeMs":1753845075890.531,"atime":"2025-07-31T01:23:59.904Z","mtime":"2025-07-30T08:39:57.745Z","ctime":"2025-07-30T08:39:57.745Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 09:24:00,113 WARN 25392 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\router.js change
2025-07-31 09:24:01,436 WARN 25392 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 09:24:01,478 WARN 25392 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 09:44:07,537 INFO 25392 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753926247524.58,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-31T01:44:07.525Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:44:07,540 INFO 25392 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753926247524.58,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-31T01:44:07.525Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:44:07,541 INFO 25392 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753926247524.58,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-31T01:44:07.525Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:44:07,541 INFO 25392 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753926247524.58,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-31T01:44:07.525Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:44:08,168 INFO 25392 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1753926248165.6648,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-07-31T01:44:08.166Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:45:36,964 INFO 25392 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1753926336960.0627,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-07-31T01:45:36.960Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:46:32,749 INFO 22320 [egg-sequelize](5ms) Executed (default): SELECT 1+1 AS result
2025-07-31 09:46:32,181 INFO 22320 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-31 09:46:32,635 INFO 22320 [egg:core] dump config after load, 5ms
2025-07-31 09:46:32,691 INFO 22320 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-31 09:46:32,691 INFO 22320 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-31 09:46:32,692 INFO 22320 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-31 09:46:32,692 INFO 22320 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-31 09:46:32,692 INFO 22320 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-31 09:46:32,692 INFO 22320 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-31 09:46:32,693 INFO 22320 [egg-watcher:agent] watcher start success
2025-07-31 09:46:32,767 INFO 22320 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [935ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [806ms] - #1 Application Start
                           ▇ [23ms] - #2 Load Plugin
                           ▇ [28ms] - #3 Load Config
                           ▇ [0ms] - #4 Require(0) config/config.default.js
                           ▇ [0ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                            ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                            ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                            ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                            ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                            ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                            ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [1ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [60ms] - #27 Load extend/agent.js
                            ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [48ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [54ms] - #31 Load extend/context.js
                              ▇ [16ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                               ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                               ▇ [22ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [41ms] - #38 Load agent.js
                               ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                                ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                ▇ [4ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [2ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [28ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [1ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [510ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇ [482ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [573ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [17ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [122ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-31 09:46:32,768 INFO 22320 [egg:core] dump config after ready, 9ms
2025-07-31 09:56:04,763 INFO 22320 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753926964751.981,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-31T01:56:04.752Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:56:04,767 INFO 22320 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753926964751.981,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-31T01:56:04.752Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:56:04,768 INFO 22320 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753926964758.4556,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-31T01:56:04.758Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:56:04,768 INFO 22320 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753926964761.139,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-31T01:56:04.761Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:56:05,405 INFO 22320 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1753926965404.0293,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-07-31T01:56:05.404Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 09:59:17,996 INFO 22320 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1753927157992.3235,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-07-31T01:59:17.992Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 10:26:50,447 INFO 3300 [egg-sequelize](3ms) Executed (default): SELECT 1+1 AS result
2025-07-31 10:26:49,838 INFO 3300 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-31 10:26:50,331 INFO 3300 [egg:core] dump config after load, 5ms
2025-07-31 10:26:50,393 INFO 3300 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-31 10:26:50,393 INFO 3300 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-31 10:26:50,394 INFO 3300 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-31 10:26:50,394 INFO 3300 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-31 10:26:50,394 INFO 3300 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-31 10:26:50,394 INFO 3300 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-31 10:26:50,395 INFO 3300 [egg-watcher:agent] watcher start success
2025-07-31 10:26:50,454 INFO 3300 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [1021ms] - #0 Process Start
                           ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [855ms] - #1 Application Start
                           ▇ [26ms] - #2 Load Plugin
                            ▇ [30ms] - #3 Load Config
                            ▇ [0ms] - #4 Require(0) config/config.default.js
                            ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                            ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                            ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                            ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                            ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                            ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                            ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [1ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [1ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [1ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [1ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [1ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [0ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                             ▇ [0ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                             ▇ [1ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                             ▇ [1ms] - #26 Require(22) node_modules/egg/config/config.local.js
                             ▇ [60ms] - #27 Load extend/agent.js
                             ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                             ▇ [47ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [63ms] - #31 Load extend/context.js
                              ▇ [22ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                               ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                               ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                               ▇ [24ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                                ▇ [1ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                                ▇ [3ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                                ▇ [43ms] - #38 Load agent.js
                                ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                                ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                ▇ [5ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [2ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [28ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [554ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇ [522ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [600ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                              ▇ [18ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [124ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-31 10:26:50,454 INFO 3300 [egg:core] dump config after ready, 5ms
2025-07-31 10:41:26,656 INFO 32264 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 10:41:26,106 INFO 32264 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-31 10:41:26,551 INFO 32264 [egg:core] dump config after load, 5ms
2025-07-31 10:41:26,607 INFO 32264 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-31 10:41:26,607 INFO 32264 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-31 10:41:26,608 INFO 32264 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-31 10:41:26,608 INFO 32264 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-31 10:41:26,608 INFO 32264 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-31 10:41:26,608 INFO 32264 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-31 10:41:26,609 INFO 32264 [egg-watcher:agent] watcher start success
2025-07-31 10:41:26,666 INFO 32264 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [899ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [791ms] - #1 Application Start
                           ▇ [23ms] - #2 Load Plugin
                           ▇ [29ms] - #3 Load Config
                           ▇ [0ms] - #4 Require(0) config/config.default.js
                           ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [0ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                           ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                           ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                            ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [0ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [0ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [1ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [1ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [1ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [58ms] - #27 Load extend/agent.js
                            ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [46ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [52ms] - #31 Load extend/context.js
                              ▇ [14ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                              ▇ [1ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                              ▇ [21ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [1ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [3ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [49ms] - #38 Load agent.js
                               ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                               ▇ [1ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                               ▇ [5ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [3ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [33ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [1ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [0ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [500ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇ [473ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [541ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [17ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [112ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-31 10:41:26,667 INFO 32264 [egg:core] dump config after ready, 6ms
2025-07-31 10:49:03,397 ERROR 32264 nodejs.ClusterClientNoResponseError: client no response in 154550ms exceeding maxIdleTime 60000ms, maybe the connection is close on other side.
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\plan-map\后端\node_modules\cluster-client\lib\leader.js:79:25)
    at listOnTimeout (node:internal/timers:581:17)
    at process.processTimers (node:internal/timers:519:7)
name: "ClusterClientNoResponseError"
pid: 32264
hostname: DESKTOP-GRM34K4

2025-07-31 10:56:04,341 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753930564333.3467,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-31T02:56:04.333Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 10:56:04,343 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753930564333.3467,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-31T02:56:04.333Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 10:56:04,347 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753930564335.8533,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-31T02:56:04.336Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 10:56:04,347 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753930564340.1458,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-31T02:56:04.340Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 11:01:22,715 ERROR 32264 nodejs.ClusterClientNoResponseError: client no response in 138927ms exceeding maxIdleTime 60000ms, maybe the connection is close on other side.
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\plan-map\后端\node_modules\cluster-client\lib\leader.js:79:25)
    at listOnTimeout (node:internal/timers:581:17)
    at process.processTimers (node:internal/timers:519:7)
name: "ClusterClientNoResponseError"
pid: 32264
hostname: DESKTOP-GRM34K4

2025-07-31 11:03:58,171 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1753931038166.9893,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-07-31T03:03:58.167Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 11:03:58,178 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1753931038174.808,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-07-31T03:03:58.175Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 11:37:36,964 WARN 32264 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-07-31 11:37:36,717 INFO 32264 [egg-watcher] Received a change event from eventSource: {"path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js","event":"change","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":18014398509862772,"size":595,"blocks":1,"atimeMs":1753933056716.7112,"mtimeMs":1753845216497.8264,"ctimeMs":1753845216497.8264,"birthtimeMs":1753845075856.6013,"atime":"2025-07-31T03:37:36.717Z","mtime":"2025-07-30T03:13:36.498Z","ctime":"2025-07-30T03:13:36.498Z","birthtime":"2025-07-30T03:11:15.857Z"}}
2025-07-31 11:37:36,741 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1753933056717.7644,"mtimeMs":1753860473756.1506,"ctimeMs":1753860473756.1506,"birthtimeMs":1753845075904.9722,"atime":"2025-07-31T03:37:36.718Z","mtime":"2025-07-30T07:27:53.756Z","ctime":"2025-07-30T07:27:53.756Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:36,742 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":902,"blocks":8,"atimeMs":1753933056717.7644,"mtimeMs":1753864797744.53,"ctimeMs":1753864797744.53,"birthtimeMs":1753845075890.531,"atime":"2025-07-31T03:37:36.718Z","mtime":"2025-07-30T08:39:57.745Z","ctime":"2025-07-30T08:39:57.745Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:36,742 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils\\tool.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":14636698789334984,"size":2765,"blocks":8,"atimeMs":1753933056722.7673,"mtimeMs":1732008650000,"ctimeMs":1753845075901.432,"birthtimeMs":1753845075900.4263,"atime":"2025-07-31T03:37:36.723Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.901Z","birthtime":"2025-07-30T03:11:15.900Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:36,744 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\plugin.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700561,"size":389,"blocks":0,"atimeMs":1753933056717.7644,"mtimeMs":1753845180012.2527,"ctimeMs":1753845180012.2527,"birthtimeMs":1753845075908.4712,"atime":"2025-07-31T03:37:36.718Z","mtime":"2025-07-30T03:13:00.012Z","ctime":"2025-07-30T03:13:00.012Z","birthtime":"2025-07-30T03:11:15.908Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:36,758 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule\\sendMessage.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697619396,"size":350,"blocks":0,"atimeMs":1753933056724.3281,"mtimeMs":1732008650000,"ctimeMs":1753845075893.994,"birthtimeMs":1753845075892.9944,"atime":"2025-07-31T03:37:36.724Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.894Z","birthtime":"2025-07-30T03:11:15.893Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:36,759 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":7881299348279197,"size":997,"blocks":8,"atimeMs":1753933056724.3281,"mtimeMs":1753848028559.5122,"ctimeMs":1753848028559.5122,"birthtimeMs":1753845075867.3916,"atime":"2025-07-31T03:37:36.724Z","mtime":"2025-07-30T04:00:28.560Z","ctime":"2025-07-30T04:00:28.560Z","birthtime":"2025-07-30T03:11:15.867Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:36,760 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3588,"blocks":8,"atimeMs":1753933056727.349,"mtimeMs":1753864094855.4304,"ctimeMs":1753864094855.4304,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T03:37:36.727Z","mtime":"2025-07-30T08:28:14.855Z","ctime":"2025-07-30T08:28:14.855Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:36,760 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4222124651529413,"size":1035,"blocks":8,"atimeMs":1753933056725.3093,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1753847979734.9976,"atime":"2025-07-31T03:37:36.725Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-07-30T03:59:39.735Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:36,760 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861084092,"size":910,"blocks":8,"atimeMs":1753933056725.3093,"mtimeMs":1753848023924.8098,"ctimeMs":1753848023924.8098,"birthtimeMs":1753848013093.8396,"atime":"2025-07-31T03:37:36.725Z","mtime":"2025-07-30T04:00:23.925Z","ctime":"2025-07-30T04:00:23.925Z","birthtime":"2025-07-30T04:00:13.094Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:36,761 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2751,"blocks":8,"atimeMs":1753933056727.349,"mtimeMs":1753864104432.0076,"ctimeMs":1753864104432.0076,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T03:37:36.727Z","mtime":"2025-07-30T08:28:24.432Z","ctime":"2025-07-30T08:28:24.432Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:36,761 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":2763,"blocks":8,"atimeMs":1753933056728.6362,"mtimeMs":1753864109877.0894,"ctimeMs":1753864109877.0894,"birthtimeMs":1753847132395.9258,"atime":"2025-07-31T03:37:36.729Z","mtime":"2025-07-30T08:28:29.877Z","ctime":"2025-07-30T08:28:29.877Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:37:40,101 WARN 32264 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 11:41:50,159 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3649,"blocks":8,"atimeMs":1753933310148.5164,"mtimeMs":1753933310148.5164,"ctimeMs":1753933310148.5164,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T03:41:50.149Z","mtime":"2025-07-31T03:41:50.149Z","ctime":"2025-07-31T03:41:50.149Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:41:50,160 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3649,"blocks":8,"atimeMs":1753933310148.5164,"mtimeMs":1753933310148.5164,"ctimeMs":1753933310148.5164,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T03:41:50.149Z","mtime":"2025-07-31T03:41:50.149Z","ctime":"2025-07-31T03:41:50.149Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:41:50,160 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3649,"blocks":8,"atimeMs":1753933310148.5164,"mtimeMs":1753933310148.5164,"ctimeMs":1753933310148.5164,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T03:41:50.149Z","mtime":"2025-07-31T03:41:50.149Z","ctime":"2025-07-31T03:41:50.149Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:41:50,364 WARN 32264 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-31 11:41:52,602 WARN 32264 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 11:42:05,069 WARN 32264 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-31 11:42:04,864 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3649,"blocks":8,"atimeMs":1753933324850.9395,"mtimeMs":1753933324850.9395,"ctimeMs":1753933324850.9395,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T03:42:04.851Z","mtime":"2025-07-31T03:42:04.851Z","ctime":"2025-07-31T03:42:04.851Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:42:04,865 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3649,"blocks":8,"atimeMs":1753933324850.9395,"mtimeMs":1753933324850.9395,"ctimeMs":1753933324850.9395,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T03:42:04.851Z","mtime":"2025-07-31T03:42:04.851Z","ctime":"2025-07-31T03:42:04.851Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:42:04,866 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3649,"blocks":8,"atimeMs":1753933324850.9395,"mtimeMs":1753933324850.9395,"ctimeMs":1753933324850.9395,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T03:42:04.851Z","mtime":"2025-07-31T03:42:04.851Z","ctime":"2025-07-31T03:42:04.851Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:42:07,265 WARN 32264 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 11:42:12,782 WARN 32264 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\location.js change
2025-07-31 11:42:12,567 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2813,"blocks":8,"atimeMs":1753933332550.6262,"mtimeMs":1753933332550.6262,"ctimeMs":1753933332550.6262,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T03:42:12.551Z","mtime":"2025-07-31T03:42:12.551Z","ctime":"2025-07-31T03:42:12.551Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:42:12,567 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2813,"blocks":8,"atimeMs":1753933332550.6262,"mtimeMs":1753933332550.6262,"ctimeMs":1753933332550.6262,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T03:42:12.551Z","mtime":"2025-07-31T03:42:12.551Z","ctime":"2025-07-31T03:42:12.551Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:42:12,567 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2813,"blocks":8,"atimeMs":1753933332550.6262,"mtimeMs":1753933332550.6262,"ctimeMs":1753933332550.6262,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T03:42:12.551Z","mtime":"2025-07-31T03:42:12.551Z","ctime":"2025-07-31T03:42:12.551Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:42:15,752 WARN 32264 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 11:43:29,890 WARN 32264 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\location.js change
2025-07-31 11:43:29,683 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2799,"blocks":8,"atimeMs":1753933409677.7515,"mtimeMs":1753933409677.7515,"ctimeMs":1753933409677.7515,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T03:43:29.678Z","mtime":"2025-07-31T03:43:29.678Z","ctime":"2025-07-31T03:43:29.678Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:43:29,683 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2799,"blocks":8,"atimeMs":1753933409677.7515,"mtimeMs":1753933409677.7515,"ctimeMs":1753933409677.7515,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T03:43:29.678Z","mtime":"2025-07-31T03:43:29.678Z","ctime":"2025-07-31T03:43:29.678Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:43:32,078 WARN 32264 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 11:43:37,532 WARN 32264 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\location.js change
2025-07-31 11:43:37,329 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2797,"blocks":8,"atimeMs":1753933417322.3918,"mtimeMs":1753933417322.3918,"ctimeMs":1753933417322.3918,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T03:43:37.322Z","mtime":"2025-07-31T03:43:37.322Z","ctime":"2025-07-31T03:43:37.322Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:43:37,330 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2797,"blocks":8,"atimeMs":1753933417322.3918,"mtimeMs":1753933417322.3918,"ctimeMs":1753933417322.3918,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T03:43:37.322Z","mtime":"2025-07-31T03:43:37.322Z","ctime":"2025-07-31T03:43:37.322Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:43:37,331 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2797,"blocks":8,"atimeMs":1753933417322.3918,"mtimeMs":1753933417322.3918,"ctimeMs":1753933417322.3918,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T03:43:37.322Z","mtime":"2025-07-31T03:43:37.322Z","ctime":"2025-07-31T03:43:37.322Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:43:39,612 WARN 32264 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 11:43:45,748 WARN 32264 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-07-31 11:43:45,543 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3606,"blocks":8,"atimeMs":1753933425539.6057,"mtimeMs":1753933425539.6057,"ctimeMs":1753933425539.6057,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T03:43:45.540Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-07-31T03:43:45.540Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:43:45,543 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3606,"blocks":8,"atimeMs":1753933425539.6057,"mtimeMs":1753933425539.6057,"ctimeMs":1753933425539.6057,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T03:43:45.540Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-07-31T03:43:45.540Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 11:43:47,951 WARN 32264 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 11:56:31,349 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753934191340.5493,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-31T03:56:31.341Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 11:56:31,354 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753934191341.5576,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-31T03:56:31.342Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 11:56:31,354 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753934191343.997,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-31T03:56:31.344Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 11:56:31,355 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753934191347.6448,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-31T03:56:31.348Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 12:56:33,150 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753937793141.278,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-31T04:56:33.141Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 12:56:33,153 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753937793141.278,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-31T04:56:33.141Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 12:56:33,153 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753937793145.283,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-31T04:56:33.145Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 12:56:33,154 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753937793145.283,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-31T04:56:33.145Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 13:56:33,642 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753941393634.104,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-31T05:56:33.634Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 13:56:33,645 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753941393634.104,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-31T05:56:33.634Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 13:56:33,646 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753941393637.608,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-31T05:56:33.638Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 13:56:33,646 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753941393637.608,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-31T05:56:33.638Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 14:56:34,007 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753944993996.489,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-31T06:56:33.996Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 14:56:34,007 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753944994001.489,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-31T06:56:34.001Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 14:56:34,010 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753944994001.489,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-31T06:56:34.001Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 14:56:34,010 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753944994006.4778,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-31T06:56:34.006Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 15:19:27,106 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1753946367102.5342,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-07-31T07:19:27.103Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 15:19:27,112 INFO 32264 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1753946367109.326,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-07-31T07:19:27.109Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 15:30:08,738 INFO 32384 [egg-sequelize](5ms) Executed (default): SELECT 1+1 AS result
2025-07-31 15:30:08,140 INFO 32384 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-07-31 15:30:08,618 INFO 32384 [egg:core] dump config after load, 6ms
2025-07-31 15:30:08,675 INFO 32384 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-07-31 15:30:08,675 INFO 32384 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-07-31 15:30:08,675 INFO 32384 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-07-31 15:30:08,676 INFO 32384 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-07-31 15:30:08,676 INFO 32384 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-07-31 15:30:08,676 INFO 32384 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-07-31 15:30:08,677 INFO 32384 [egg-watcher:agent] watcher start success
2025-07-31 15:30:08,755 INFO 32384 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [986ms] - #0 Process Start
                          ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [852ms] - #1 Application Start
                           ▇ [25ms] - #2 Load Plugin
                           ▇ [30ms] - #3 Load Config
                           ▇ [1ms] - #4 Require(0) config/config.default.js
                            ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                            ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                            ▇ [1ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                            ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                            ▇ [0ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                            ▇ [0ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                            ▇ [0ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                            ▇ [0ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                            ▇ [1ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                            ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                            ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                            ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                            ▇ [0ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                            ▇ [0ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                            ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                            ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                            ▇ [0ms] - #21 Require(17) config/config.default.js
                            ▇ [0ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                            ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                            ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [63ms] - #27 Load extend/agent.js
                            ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [50ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                              ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                              ▇ [58ms] - #31 Load extend/context.js
                              ▇ [15ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                               ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                               ▇ [27ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [2ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [3ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                                ▇ [43ms] - #38 Load agent.js
                                ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                                ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                                ▇ [4ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                                ▇ [2ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                                ▇ [29ms] - #43 Require(36) node_modules/egg-development/agent.js
                                 ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                 ▇ [1ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                 ▇ [1ms] - #46 Require(39) node_modules/egg/agent.js
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [534ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇ [505ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                 ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [605ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [18ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [127ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-07-31 15:30:08,755 INFO 32384 [egg:core] dump config after ready, 5ms
2025-07-31 15:57:02,065 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753948622057.036,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-31T07:57:02.057Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 15:57:02,067 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753948622059.2024,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-31T07:57:02.059Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 15:57:02,070 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753948622062.3496,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-31T07:57:02.062Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 15:57:02,071 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753948622065.6729,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-31T07:57:02.066Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 16:26:05,315 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1753950365311.9443,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-07-31T08:26:05.312Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 16:26:05,322 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1753950365318.9458,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-07-31T08:26:05.319Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 16:57:02,381 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1753952222372.5693,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-07-31T08:57:02.373Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 16:57:02,384 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1753952222374.9692,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-07-31T08:57:02.375Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 16:57:02,384 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1753952222376.546,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-07-31T08:57:02.377Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 16:57:02,385 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1753952222379.8303,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-07-31T08:57:02.380Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-07-31 17:00:47,623 WARN 32384 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\location.js change
2025-07-31 17:00:47,412 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3606,"blocks":8,"atimeMs":1753952447402.62,"mtimeMs":1753933425539.6057,"ctimeMs":1753933425539.6057,"birthtimeMs":1753847102917.4177,"atime":"2025-07-31T09:00:47.403Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-07-31T03:43:45.540Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 17:00:47,417 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2797,"blocks":8,"atimeMs":1753952447408.0325,"mtimeMs":1753933417322.3918,"ctimeMs":1753933417322.3918,"birthtimeMs":1753847118676.5964,"atime":"2025-07-31T09:00:47.408Z","mtime":"2025-07-31T03:43:37.322Z","ctime":"2025-07-31T03:43:37.322Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 17:00:49,105 WARN 32384 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\config\config.default.js change
2025-07-31 17:00:48,869 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\plugin.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700561,"size":389,"blocks":0,"atimeMs":1753952448866.688,"mtimeMs":1753845180012.2527,"ctimeMs":1753845180012.2527,"birthtimeMs":1753845075908.4712,"atime":"2025-07-31T09:00:48.867Z","mtime":"2025-07-30T03:13:00.012Z","ctime":"2025-07-30T03:13:00.012Z","birthtime":"2025-07-30T03:11:15.908Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 17:00:48,898 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1753952448888.5583,"mtimeMs":1753860473756.1506,"ctimeMs":1753860473756.1506,"birthtimeMs":1753845075904.9722,"atime":"2025-07-31T09:00:48.889Z","mtime":"2025-07-30T07:27:53.756Z","ctime":"2025-07-30T07:27:53.756Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 17:00:49,455 WARN 32384 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js change
2025-07-31 17:00:49,191 INFO 32384 [egg-watcher] Received a change event from eventSource: {"path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js","event":"change","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":18014398509862772,"size":595,"blocks":1,"atimeMs":1753952449190.0273,"mtimeMs":1753845216497.8264,"ctimeMs":1753845216497.8264,"birthtimeMs":1753845075856.6013,"atime":"2025-07-31T09:00:49.190Z","mtime":"2025-07-30T03:13:36.498Z","ctime":"2025-07-30T03:13:36.498Z","birthtime":"2025-07-30T03:11:15.857Z"}}
2025-07-31 17:00:49,246 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule\\sendMessage.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697619396,"size":350,"blocks":0,"atimeMs":1753952449240.0054,"mtimeMs":1732008650000,"ctimeMs":1753845075893.994,"birthtimeMs":1753845075892.9944,"atime":"2025-07-31T09:00:49.240Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.894Z","birthtime":"2025-07-30T03:11:15.893Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 17:00:49,818 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":7881299348279197,"size":997,"blocks":8,"atimeMs":1753952449814.577,"mtimeMs":1753848028559.5122,"ctimeMs":1753848028559.5122,"birthtimeMs":1753845075867.3916,"atime":"2025-07-31T09:00:49.815Z","mtime":"2025-07-30T04:00:28.560Z","ctime":"2025-07-30T04:00:28.560Z","birthtime":"2025-07-30T03:11:15.867Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 17:00:49,832 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4222124651529413,"size":1035,"blocks":8,"atimeMs":1753952449824.528,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1753847979734.9976,"atime":"2025-07-31T09:00:49.825Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-07-30T03:59:39.735Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 17:00:49,833 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861084092,"size":910,"blocks":8,"atimeMs":1753952449824.528,"mtimeMs":1753848023924.8098,"ctimeMs":1753848023924.8098,"birthtimeMs":1753848013093.8396,"atime":"2025-07-31T09:00:49.825Z","mtime":"2025-07-30T04:00:23.925Z","ctime":"2025-07-30T04:00:23.925Z","birthtime":"2025-07-30T04:00:13.094Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 17:00:50,026 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":2763,"blocks":8,"atimeMs":1753952450016.41,"mtimeMs":1753864109877.0894,"ctimeMs":1753864109877.0894,"birthtimeMs":1753847132395.9258,"atime":"2025-07-31T09:00:50.016Z","mtime":"2025-07-30T08:28:29.877Z","ctime":"2025-07-30T08:28:29.877Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 17:00:50,027 INFO 32384 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":902,"blocks":8,"atimeMs":1753952450022.3596,"mtimeMs":1753864797744.53,"ctimeMs":1753864797744.53,"birthtimeMs":1753845075890.531,"atime":"2025-07-31T09:00:50.022Z","mtime":"2025-07-30T08:39:57.745Z","ctime":"2025-07-30T08:39:57.745Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-07-31 17:00:50,103 WARN 32384 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 17:00:50,228 WARN 32384 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\router.js change
2025-07-31 17:00:50,153 WARN 32384 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-07-31 17:00:52,618 WARN 32384 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 08:43:07,867 INFO 22624 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-01 08:43:08,429 INFO 22624 [egg:core] dump config after load, 6ms
2025-08-01 08:43:08,494 INFO 22624 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-08-01 08:43:08,494 INFO 22624 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-08-01 08:43:08,494 INFO 22624 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-08-01 08:43:08,495 INFO 22624 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-08-01 08:43:08,495 INFO 22624 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-08-01 08:43:08,495 INFO 22624 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-08-01 08:43:08,496 INFO 22624 [egg-watcher:agent] watcher start success
2025-08-01 08:43:08,506 WARN 22624 Sequelize Error: , sleep 1 seconds to retry...
2025-08-01 08:43:09,521 WARN 22624 Sequelize Error: , sleep 1 seconds to retry...
2025-08-01 08:43:10,530 WARN 22624 Sequelize Error: , sleep 1 seconds to retry...
2025-08-01 08:47:48,142 WARN 18148 Sequelize Error: , sleep 1 seconds to retry...
2025-08-01 08:47:47,562 INFO 18148 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-01 08:47:48,053 INFO 18148 [egg:core] dump config after load, 6ms
2025-08-01 08:47:48,126 INFO 18148 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-08-01 08:47:48,126 INFO 18148 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-08-01 08:47:48,126 INFO 18148 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-08-01 08:47:48,127 INFO 18148 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-08-01 08:47:48,127 INFO 18148 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-08-01 08:47:48,127 INFO 18148 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-08-01 08:47:48,128 INFO 18148 [egg-watcher:agent] watcher start success
2025-08-01 08:47:49,149 WARN 18148 Sequelize Error: , sleep 1 seconds to retry...
2025-08-01 08:47:50,163 WARN 18148 Sequelize Error: , sleep 1 seconds to retry...
2025-08-01 08:48:19,296 INFO 5592 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 08:48:18,711 INFO 5592 [egg:logger] init all loggers with options: {"dir":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\logs\\problem-node","encoding":"utf8","env":"local","level":"INFO","consoleLevel":"INFO","disableConsoleAfterReady":false,"outputJSON":false,"buffer":true,"appLogName":"problem-node-web.log","coreLogName":"egg-web.log","agentLogName":"egg-agent.log","errorLogName":"common-error.log","coreLogger":{"consoleLevel":"WARN"},"allowDebugAtProd":false,"enablePerformanceTimer":false,"enableFastContextLogger":false,"type":"agent","localStorage":{"enabled":false}}
2025-08-01 08:48:19,182 INFO 5592 [egg:core] dump config after load, 5ms
2025-08-01 08:48:19,236 INFO 5592 [egg-watcher] Start watching: ["C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy","C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"]
2025-08-01 08:48:19,236 INFO 5592 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app"
2025-08-01 08:48:19,237 INFO 5592 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config"
2025-08-01 08:48:19,237 INFO 5592 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks"
2025-08-01 08:48:19,237 INFO 5592 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\mocks_proxy"
2025-08-01 08:48:19,237 INFO 5592 [egg-watcher] Start watching: "C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js"
2025-08-01 08:48:19,238 INFO 5592 [egg-watcher:agent] watcher start success
2025-08-01 08:48:19,308 INFO 5592 egg start timeline:
▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [898ms] - #0 Process Start
                         ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [825ms] - #1 Application Start
                          ▇ [22ms] - #2 Load Plugin
                           ▇ [34ms] - #3 Load Config
                           ▇ [1ms] - #4 Require(0) config/config.default.js
                           ▇ [1ms] - #5 Require(1) node_modules/egg-session/config/config.default.js
                           ▇ [1ms] - #6 Require(2) node_modules/egg-security/config/config.default.js
                           ▇ [0ms] - #7 Require(3) node_modules/egg-jsonp/config/config.default.js
                           ▇ [1ms] - #8 Require(4) node_modules/egg-onerror/config/config.default.js
                           ▇ [1ms] - #9 Require(5) node_modules/egg-i18n/config/config.default.js
                           ▇ [1ms] - #10 Require(6) node_modules/egg-watcher/config/config.default.js
                           ▇ [1ms] - #11 Require(7) node_modules/egg-schedule/config/config.default.js
                           ▇ [1ms] - #12 Require(8) node_modules/egg-multipart/config/config.default.js
                           ▇ [0ms] - #13 Require(9) node_modules/egg-development/config/config.default.js
                           ▇ [1ms] - #14 Require(10) node_modules/egg-logrotator/config/config.default.js
                           ▇ [1ms] - #15 Require(11) node_modules/egg-static/config/config.default.js
                           ▇ [0ms] - #16 Require(12) node_modules/egg-view/config/config.default.js
                           ▇ [1ms] - #17 Require(13) node_modules/egg-sequelize/config/config.default.js
                           ▇ [1ms] - #18 Require(14) node_modules/egg-cors/config/config.default.js
                           ▇ [0ms] - #19 Require(15) node_modules/egg-view-nunjucks/config/config.default.js
                           ▇ [0ms] - #20 Require(16) node_modules/egg/config/config.default.js
                           ▇ [0ms] - #21 Require(17) config/config.default.js
                           ▇ [1ms] - #22 Require(18) node_modules/egg-security/config/config.local.js
                           ▇ [1ms] - #23 Require(19) node_modules/egg-watcher/config/config.local.js
                           ▇ [1ms] - #24 Require(20) node_modules/egg-view/config/config.local.js
                            ▇ [0ms] - #25 Require(21) node_modules/egg-view-nunjucks/config/config.local.js
                            ▇ [0ms] - #26 Require(22) node_modules/egg/config/config.local.js
                            ▇ [58ms] - #27 Load extend/agent.js
                            ▇ [2ms] - #28 Require(23) node_modules/egg-security/app/extend/agent.js
                            ▇ [47ms] - #29 Require(24) node_modules/egg-schedule/app/extend/agent.js
                             ▇ [2ms] - #30 Require(25) node_modules/egg-logrotator/app/extend/agent.js
                             ▇ [56ms] - #31 Load extend/context.js
                             ▇ [18ms] - #32 Require(26) node_modules/egg-security/app/extend/context.js
                              ▇ [3ms] - #33 Require(27) node_modules/egg-jsonp/app/extend/context.js
                              ▇ [0ms] - #34 Require(28) node_modules/egg-i18n/app/extend/context.js
                              ▇ [22ms] - #35 Require(29) node_modules/egg-multipart/app/extend/context.js
                               ▇ [1ms] - #36 Require(30) node_modules/egg-view/app/extend/context.js
                               ▇ [2ms] - #37 Require(31) node_modules/egg/app/extend/context.js
                               ▇ [41ms] - #38 Load agent.js
                               ▇ [1ms] - #39 Require(32) node_modules/egg-security/agent.js
                               ▇ [0ms] - #40 Require(33) node_modules/egg-onerror/agent.js
                               ▇ [4ms] - #41 Require(34) node_modules/egg-watcher/agent.js
                               ▇ [2ms] - #42 Require(35) node_modules/egg-schedule/agent.js
                               ▇ [27ms] - #43 Require(36) node_modules/egg-development/agent.js
                                ▇ [0ms] - #44 Require(37) node_modules/egg-logrotator/agent.js
                                ▇ [0ms] - #45 Require(38) node_modules/egg-sequelize/agent.js
                                ▇ [1ms] - #46 Require(39) node_modules/egg/agent.js
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [524ms] - #47 Before Start in node_modules/egg-watcher/lib/init.js:15:14
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [497ms] - #48 Before Start in node_modules/egg-schedule/agent.js:12:9
                                ▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇ [526ms] - #49 Before Start in node_modules/egg-development/agent.js:9:9
                                             ▇ [17ms] - #50 Load "Symbol(model)" to Application
                                              ▇▇▇ [122ms] - #51 Before Start in node_modules/egg-sequelize/lib/loader.js:39:7
2025-08-01 08:48:19,308 INFO 5592 [egg:core] dump config after ready, 6ms
2025-08-01 08:56:07,928 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-08-01 08:56:07,717 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3147,"blocks":8,"atimeMs":1754009767701.3352,"mtimeMs":1754009767701.3352,"ctimeMs":1754009767701.3352,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:56:07.701Z","mtime":"2025-08-01T00:56:07.701Z","ctime":"2025-08-01T00:56:07.701Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:56:07,719 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3147,"blocks":8,"atimeMs":1754009767701.3352,"mtimeMs":1754009767701.3352,"ctimeMs":1754009767701.3352,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:56:07.701Z","mtime":"2025-08-01T00:56:07.701Z","ctime":"2025-08-01T00:56:07.701Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:56:07,720 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3147,"blocks":8,"atimeMs":1754009767701.3352,"mtimeMs":1754009767701.3352,"ctimeMs":1754009767701.3352,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:56:07.701Z","mtime":"2025-08-01T00:56:07.701Z","ctime":"2025-08-01T00:56:07.701Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:56:09,991 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 08:57:02,259 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-08-01 08:57:02,047 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3414,"blocks":8,"atimeMs":1754009822042.312,"mtimeMs":1754009822042.312,"ctimeMs":1754009822042.312,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:57:02.042Z","mtime":"2025-08-01T00:57:02.042Z","ctime":"2025-08-01T00:57:02.042Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:57:02,048 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3414,"blocks":8,"atimeMs":1754009822042.312,"mtimeMs":1754009822042.312,"ctimeMs":1754009822042.312,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:57:02.042Z","mtime":"2025-08-01T00:57:02.042Z","ctime":"2025-08-01T00:57:02.042Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:57:04,305 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 08:57:11,412 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3412,"blocks":8,"atimeMs":1754009831403.0308,"mtimeMs":1754009831403.0308,"ctimeMs":1754009831403.0308,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:57:11.403Z","mtime":"2025-08-01T00:57:11.403Z","ctime":"2025-08-01T00:57:11.403Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:57:11,413 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3412,"blocks":8,"atimeMs":1754009831403.0308,"mtimeMs":1754009831403.0308,"ctimeMs":1754009831403.0308,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:57:11.403Z","mtime":"2025-08-01T00:57:11.403Z","ctime":"2025-08-01T00:57:11.403Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:57:11,413 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3412,"blocks":8,"atimeMs":1754009831403.0308,"mtimeMs":1754009831403.0308,"ctimeMs":1754009831403.0308,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:57:11.403Z","mtime":"2025-08-01T00:57:11.403Z","ctime":"2025-08-01T00:57:11.403Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:57:11,617 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-08-01 08:57:13,664 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 08:57:25,829 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-08-01 08:57:25,625 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3414,"blocks":8,"atimeMs":1754009845615.484,"mtimeMs":1754009845615.484,"ctimeMs":1754009845615.484,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:57:25.615Z","mtime":"2025-08-01T00:57:25.615Z","ctime":"2025-08-01T00:57:25.615Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:57:25,625 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3414,"blocks":8,"atimeMs":1754009845615.484,"mtimeMs":1754009845615.484,"ctimeMs":1754009845615.484,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:57:25.615Z","mtime":"2025-08-01T00:57:25.615Z","ctime":"2025-08-01T00:57:25.615Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:57:25,626 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":3414,"blocks":8,"atimeMs":1754009845615.484,"mtimeMs":1754009845615.484,"ctimeMs":1754009845615.484,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:57:25.615Z","mtime":"2025-08-01T00:57:25.615Z","ctime":"2025-08-01T00:57:25.615Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:57:27,811 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 08:59:21,954 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-08-01 08:59:21,750 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4260,"blocks":16,"atimeMs":1754009961745.8696,"mtimeMs":1754009961745.8696,"ctimeMs":1754009961745.8696,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:59:21.746Z","mtime":"2025-08-01T00:59:21.746Z","ctime":"2025-08-01T00:59:21.746Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:59:21,751 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4260,"blocks":16,"atimeMs":1754009961745.8696,"mtimeMs":1754009961745.8696,"ctimeMs":1754009961745.8696,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:59:21.746Z","mtime":"2025-08-01T00:59:21.746Z","ctime":"2025-08-01T00:59:21.746Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:59:21,751 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4260,"blocks":16,"atimeMs":1754009961745.8696,"mtimeMs":1754009961745.8696,"ctimeMs":1754009961745.8696,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:59:21.746Z","mtime":"2025-08-01T00:59:21.746Z","ctime":"2025-08-01T00:59:21.746Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:59:23,978 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 08:59:26,099 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-08-01 08:59:25,889 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4260,"blocks":16,"atimeMs":1754009965887.9465,"mtimeMs":1754009965887.9465,"ctimeMs":1754009965887.9465,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:59:25.888Z","mtime":"2025-08-01T00:59:25.888Z","ctime":"2025-08-01T00:59:25.888Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:59:25,889 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4260,"blocks":16,"atimeMs":1754009965887.9465,"mtimeMs":1754009965887.9465,"ctimeMs":1754009965887.9465,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T00:59:25.888Z","mtime":"2025-08-01T00:59:25.888Z","ctime":"2025-08-01T00:59:25.888Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 08:59:28,062 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 09:26:00,651 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\router.js change
2025-08-01 09:26:00,449 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1015,"blocks":8,"atimeMs":1754011560432.5923,"mtimeMs":1754011560432.5923,"ctimeMs":1754011560432.5923,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T01:26:00.433Z","mtime":"2025-08-01T01:26:00.433Z","ctime":"2025-08-01T01:26:00.433Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 09:26:00,449 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1015,"blocks":8,"atimeMs":1754011560432.5923,"mtimeMs":1754011560432.5923,"ctimeMs":1754011560432.5923,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T01:26:00.433Z","mtime":"2025-08-01T01:26:00.433Z","ctime":"2025-08-01T01:26:00.433Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 09:26:00,449 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1015,"blocks":8,"atimeMs":1754011560432.5923,"mtimeMs":1754011560432.5923,"ctimeMs":1754011560432.5923,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T01:26:00.433Z","mtime":"2025-08-01T01:26:00.433Z","ctime":"2025-08-01T01:26:00.433Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 09:26:02,669 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 09:37:57,131 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1754012277126.916,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-08-01T01:37:57.127Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 09:37:57,138 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1754012277135.0613,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-08-01T01:37:57.135Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 09:42:30,197 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1754012550188.7395,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-08-01T01:42:30.189Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 09:42:30,200 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1754012550191.0317,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-08-01T01:42:30.191Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 09:42:30,200 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1754012550192.0342,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-08-01T01:42:30.192Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 09:42:30,200 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1754012550195.406,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-08-01T01:42:30.195Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:04:47,575 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-08-01 10:04:47,373 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4260,"blocks":16,"atimeMs":1754013887370.256,"mtimeMs":1754009965887.9465,"ctimeMs":1754009965887.9465,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T02:04:47.370Z","mtime":"2025-08-01T00:59:25.888Z","ctime":"2025-08-01T00:59:25.888Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 10:04:47,747 INFO 5592 [egg-watcher] Received a change event from eventSource: {"path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js","event":"change","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":18014398509862772,"size":595,"blocks":1,"atimeMs":1754013887746.046,"mtimeMs":1753845216497.8264,"ctimeMs":1753845216497.8264,"birthtimeMs":1753845075856.6013,"atime":"2025-08-01T02:04:47.746Z","mtime":"2025-07-30T03:13:36.498Z","ctime":"2025-07-30T03:13:36.498Z","birthtime":"2025-07-30T03:11:15.857Z"}}
2025-08-01 10:04:47,956 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app.js change
2025-08-01 10:04:48,454 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js change
2025-08-01 10:04:48,251 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3606,"blocks":8,"atimeMs":1754013888248.028,"mtimeMs":1753933425539.6057,"ctimeMs":1753933425539.6057,"birthtimeMs":1753847102917.4177,"atime":"2025-08-01T02:04:48.248Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-07-31T03:43:45.540Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 10:04:48,790 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\plugin.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700561,"size":389,"blocks":0,"atimeMs":1754013888786.9624,"mtimeMs":1753845180012.2527,"ctimeMs":1753845180012.2527,"birthtimeMs":1753845075908.4712,"atime":"2025-08-01T02:04:48.787Z","mtime":"2025-07-30T03:13:00.012Z","ctime":"2025-07-30T03:13:00.012Z","birthtime":"2025-07-30T03:11:15.908Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 10:04:48,813 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1754013888810.3867,"mtimeMs":1753860473756.1506,"ctimeMs":1753860473756.1506,"birthtimeMs":1753845075904.9722,"atime":"2025-08-01T02:04:48.810Z","mtime":"2025-07-30T07:27:53.756Z","ctime":"2025-07-30T07:27:53.756Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 10:04:49,220 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\model\user.js change
2025-08-01 10:04:49,013 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2797,"blocks":8,"atimeMs":1754013888994.6292,"mtimeMs":1753933417322.3918,"ctimeMs":1753933417322.3918,"birthtimeMs":1753847118676.5964,"atime":"2025-08-01T02:04:48.995Z","mtime":"2025-07-31T03:43:37.322Z","ctime":"2025-07-31T03:43:37.322Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 10:04:49,013 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":7881299348279197,"size":997,"blocks":8,"atimeMs":1754013888997.5205,"mtimeMs":1753848028559.5122,"ctimeMs":1753848028559.5122,"birthtimeMs":1753845075867.3916,"atime":"2025-08-01T02:04:48.998Z","mtime":"2025-07-30T04:00:28.560Z","ctime":"2025-07-30T04:00:28.560Z","birthtime":"2025-07-30T03:11:15.867Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 10:04:49,013 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils\\tool.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":14636698789334984,"size":2765,"blocks":8,"atimeMs":1754013888999.5056,"mtimeMs":1732008650000,"ctimeMs":1753845075901.432,"birthtimeMs":1753845075900.4263,"atime":"2025-08-01T02:04:49.000Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.901Z","birthtime":"2025-07-30T03:11:15.900Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 10:04:49,014 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule\\sendMessage.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697619396,"size":350,"blocks":0,"atimeMs":1754013888999.5056,"mtimeMs":1732008650000,"ctimeMs":1753845075893.994,"birthtimeMs":1753845075892.9944,"atime":"2025-08-01T02:04:49.000Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.894Z","birthtime":"2025-07-30T03:11:15.893Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 10:04:49,014 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4222124651529413,"size":1035,"blocks":8,"atimeMs":1754013888998.5427,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1753847979734.9976,"atime":"2025-08-01T02:04:48.999Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-07-30T03:59:39.735Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 10:04:49,015 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861084092,"size":910,"blocks":8,"atimeMs":1754013888999.5056,"mtimeMs":1753848023924.8098,"ctimeMs":1753848023924.8098,"birthtimeMs":1753848013093.8396,"atime":"2025-08-01T02:04:49.000Z","mtime":"2025-07-30T04:00:23.925Z","ctime":"2025-07-30T04:00:23.925Z","birthtime":"2025-07-30T04:00:13.094Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 10:04:49,914 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 10:04:49,966 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 10:37:57,595 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1754015877591.2068,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-08-01T02:37:57.591Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:37:57,602 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1754015877599.2075,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-08-01T02:37:57.599Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:42:30,517 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1754016150509.925,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-08-01T02:42:30.510Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:42:30,517 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1754016150511.3142,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-08-01T02:42:30.511Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:42:30,520 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1754016150513.3364,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-08-01T02:42:30.513Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:42:30,521 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1754016150517.5212,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-08-01T02:42:30.518Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:55:30,687 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1754016930678.9045,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-08-01T02:55:30.679Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:55:30,688 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1754016930680.4746,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-08-01T02:55:30.680Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:55:30,690 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1754016930684.2083,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-08-01T02:55:30.684Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:55:30,691 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1754016930687.6855,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-08-01T02:55:30.688Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:56:19,160 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1754016979154.1785,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-08-01T02:56:19.154Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 10:56:19,169 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1754016979165.3704,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-08-01T02:56:19.165Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 11:09:18,700 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\config\plugin.js change
2025-08-01 11:09:18,384 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2797,"blocks":8,"atimeMs":1754017758381.63,"mtimeMs":1753933417322.3918,"ctimeMs":1753933417322.3918,"birthtimeMs":1753847118676.5964,"atime":"2025-08-01T03:09:18.382Z","mtime":"2025-07-31T03:43:37.322Z","ctime":"2025-07-31T03:43:37.322Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:18,450 INFO 5592 [egg-watcher] Received a change event from eventSource: {"path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js","event":"change","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":18014398509862772,"size":595,"blocks":1,"atimeMs":1754014220830.6006,"mtimeMs":1753845216497.8264,"ctimeMs":1753845216497.8264,"birthtimeMs":1753845075856.6013,"atime":"2025-08-01T02:10:20.831Z","mtime":"2025-07-30T03:13:36.498Z","ctime":"2025-07-30T03:13:36.498Z","birthtime":"2025-07-30T03:11:15.857Z"}}
2025-08-01 11:09:18,484 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1015,"blocks":8,"atimeMs":1754015108810.2114,"mtimeMs":1754011560432.5923,"ctimeMs":1754011560432.5923,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T02:25:08.810Z","mtime":"2025-08-01T01:26:00.433Z","ctime":"2025-08-01T01:26:00.433Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:18,485 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3606,"blocks":8,"atimeMs":1754014221491.0593,"mtimeMs":1753933425539.6057,"ctimeMs":1753933425539.6057,"birthtimeMs":1753847102917.4177,"atime":"2025-08-01T02:10:21.491Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-07-31T03:43:45.540Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:18,485 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":7881299348279197,"size":997,"blocks":8,"atimeMs":1754014222229.6355,"mtimeMs":1753848028559.5122,"ctimeMs":1753848028559.5122,"birthtimeMs":1753845075867.3916,"atime":"2025-08-01T02:10:22.230Z","mtime":"2025-07-30T04:00:28.560Z","ctime":"2025-07-30T04:00:28.560Z","birthtime":"2025-07-30T03:11:15.867Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:18,486 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule\\sendMessage.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697619396,"size":350,"blocks":0,"atimeMs":1754014222232.7002,"mtimeMs":1732008650000,"ctimeMs":1753845075893.994,"birthtimeMs":1753845075892.9944,"atime":"2025-08-01T02:10:22.233Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.894Z","birthtime":"2025-07-30T03:11:15.893Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:18,486 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils\\tool.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":14636698789334984,"size":2765,"blocks":8,"atimeMs":1754014222234.3145,"mtimeMs":1732008650000,"ctimeMs":1753845075901.432,"birthtimeMs":1753845075900.4263,"atime":"2025-08-01T02:10:22.234Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.901Z","birthtime":"2025-07-30T03:11:15.900Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:18,487 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1754016484274.3667,"mtimeMs":1753860473756.1506,"ctimeMs":1753860473756.1506,"birthtimeMs":1753845075904.9722,"atime":"2025-08-01T02:48:04.274Z","mtime":"2025-07-30T07:27:53.756Z","ctime":"2025-07-30T07:27:53.756Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:18,487 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4260,"blocks":16,"atimeMs":1754014795923.3118,"mtimeMs":1754009965887.9465,"ctimeMs":1754009965887.9465,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T02:19:55.923Z","mtime":"2025-08-01T00:59:25.888Z","ctime":"2025-08-01T00:59:25.888Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:18,488 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4222124651529413,"size":1035,"blocks":8,"atimeMs":1754014222229.6355,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1753847979734.9976,"atime":"2025-08-01T02:10:22.230Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-07-30T03:59:39.735Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:18,488 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861084092,"size":910,"blocks":8,"atimeMs":1754014222231.6775,"mtimeMs":1753848023924.8098,"ctimeMs":1753848023924.8098,"birthtimeMs":1753848013093.8396,"atime":"2025-08-01T02:10:22.232Z","mtime":"2025-07-30T04:00:23.925Z","ctime":"2025-07-30T04:00:23.925Z","birthtime":"2025-07-30T04:00:13.094Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:18,490 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\plugin.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700561,"size":389,"blocks":0,"atimeMs":1754016484293.769,"mtimeMs":1753845180012.2527,"ctimeMs":1753845180012.2527,"birthtimeMs":1753845075908.4712,"atime":"2025-08-01T02:48:04.294Z","mtime":"2025-07-30T03:13:00.012Z","ctime":"2025-07-30T03:13:00.012Z","birthtime":"2025-07-30T03:11:15.908Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:21,247 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 11:09:41,493 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-08-01 11:09:41,290 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4340,"blocks":16,"atimeMs":1754017781275.2236,"mtimeMs":1754017781275.2236,"ctimeMs":1754017781275.2236,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T03:09:41.275Z","mtime":"2025-08-01T03:09:41.275Z","ctime":"2025-08-01T03:09:41.275Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:41,291 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4340,"blocks":16,"atimeMs":1754017781275.2236,"mtimeMs":1754017781275.2236,"ctimeMs":1754017781275.2236,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T03:09:41.275Z","mtime":"2025-08-01T03:09:41.275Z","ctime":"2025-08-01T03:09:41.275Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:41,291 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4340,"blocks":16,"atimeMs":1754017781275.2236,"mtimeMs":1754017781275.2236,"ctimeMs":1754017781275.2236,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T03:09:41.275Z","mtime":"2025-08-01T03:09:41.275Z","ctime":"2025-08-01T03:09:41.275Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:09:43,610 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 11:10:03,916 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-08-01 11:10:03,714 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4553,"blocks":16,"atimeMs":1754017803695.2295,"mtimeMs":1754017803695.2295,"ctimeMs":1754017803695.2295,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T03:10:03.695Z","mtime":"2025-08-01T03:10:03.695Z","ctime":"2025-08-01T03:10:03.695Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:03,714 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4553,"blocks":16,"atimeMs":1754017803695.2295,"mtimeMs":1754017803695.2295,"ctimeMs":1754017803695.2295,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T03:10:03.695Z","mtime":"2025-08-01T03:10:03.695Z","ctime":"2025-08-01T03:10:03.695Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:03,714 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4553,"blocks":16,"atimeMs":1754017803695.2295,"mtimeMs":1754017803695.2295,"ctimeMs":1754017803695.2295,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T03:10:03.695Z","mtime":"2025-08-01T03:10:03.695Z","ctime":"2025-08-01T03:10:03.695Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:06,140 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 11:10:46,022 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\user.js change
2025-08-01 11:10:45,811 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4551,"blocks":16,"atimeMs":1754017845796.2656,"mtimeMs":1754017845796.2656,"ctimeMs":1754017845796.2656,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T03:10:45.796Z","mtime":"2025-08-01T03:10:45.796Z","ctime":"2025-08-01T03:10:45.796Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:45,811 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4551,"blocks":16,"atimeMs":1754017845796.2656,"mtimeMs":1754017845796.2656,"ctimeMs":1754017845796.2656,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T03:10:45.796Z","mtime":"2025-08-01T03:10:45.796Z","ctime":"2025-08-01T03:10:45.796Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:45,811 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4551,"blocks":16,"atimeMs":1754017845796.2656,"mtimeMs":1754017845796.2656,"ctimeMs":1754017845796.2656,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T03:10:45.796Z","mtime":"2025-08-01T03:10:45.796Z","ctime":"2025-08-01T03:10:45.796Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:47,445 INFO 5592 [egg-watcher] Received a change event from eventSource: {"path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js","event":"change","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":18014398509862772,"size":595,"blocks":1,"atimeMs":1754017847442.3613,"mtimeMs":1753845216497.8264,"ctimeMs":1753845216497.8264,"birthtimeMs":1753845075856.6013,"atime":"2025-08-01T03:10:47.442Z","mtime":"2025-07-30T03:13:36.498Z","ctime":"2025-07-30T03:13:36.498Z","birthtime":"2025-07-30T03:11:15.857Z"}}
2025-08-01 11:10:47,495 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule\\sendMessage.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697619396,"size":350,"blocks":0,"atimeMs":1754017847492.3806,"mtimeMs":1732008650000,"ctimeMs":1753845075893.994,"birthtimeMs":1753845075892.9944,"atime":"2025-08-01T03:10:47.492Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.894Z","birthtime":"2025-07-30T03:11:15.893Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:47,696 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\schedule\sendMessage.js change
2025-08-01 11:10:48,379 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\location.js change
2025-08-01 11:10:47,994 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":7881299348279197,"size":997,"blocks":8,"atimeMs":1754017847989.2683,"mtimeMs":1753848028559.5122,"ctimeMs":1753848028559.5122,"birthtimeMs":1753845075867.3916,"atime":"2025-08-01T03:10:47.989Z","mtime":"2025-07-30T04:00:28.560Z","ctime":"2025-07-30T04:00:28.560Z","birthtime":"2025-07-30T03:11:15.867Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:48,003 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4222124651529413,"size":1035,"blocks":8,"atimeMs":1754017847998.2717,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1753847979734.9976,"atime":"2025-08-01T03:10:47.998Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-07-30T03:59:39.735Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:48,004 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861084092,"size":910,"blocks":8,"atimeMs":1754017847999.784,"mtimeMs":1753848023924.8098,"ctimeMs":1753848023924.8098,"birthtimeMs":1753848013093.8396,"atime":"2025-08-01T03:10:48.000Z","mtime":"2025-07-30T04:00:23.925Z","ctime":"2025-07-30T04:00:23.925Z","birthtime":"2025-07-30T04:00:13.094Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:48,174 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3606,"blocks":8,"atimeMs":1754017848167.5293,"mtimeMs":1753933425539.6057,"ctimeMs":1753933425539.6057,"birthtimeMs":1753847102917.4177,"atime":"2025-08-01T03:10:48.168Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-07-31T03:43:45.540Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:48,174 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2797,"blocks":8,"atimeMs":1754017848169.6704,"mtimeMs":1753933417322.3918,"ctimeMs":1753933417322.3918,"birthtimeMs":1753847118676.5964,"atime":"2025-08-01T03:10:48.170Z","mtime":"2025-07-31T03:43:37.322Z","ctime":"2025-07-31T03:43:37.322Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:10:48,259 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 11:10:48,312 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 11:10:50,642 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 11:24:45,228 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\utils\tool.js change
2025-08-01 11:24:45,026 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils\\tool.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":14636698789334984,"size":2765,"blocks":8,"atimeMs":1754018685022.7283,"mtimeMs":1732008650000,"ctimeMs":1753845075901.432,"birthtimeMs":1753845075900.4263,"atime":"2025-08-01T03:24:45.023Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.901Z","birthtime":"2025-07-30T03:11:15.900Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:24:47,345 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 11:29:10,743 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1015,"blocks":8,"atimeMs":1754018950735.8562,"mtimeMs":1754011560432.5923,"ctimeMs":1754011560432.5923,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T03:29:10.736Z","mtime":"2025-08-01T01:26:00.433Z","ctime":"2025-08-01T01:26:00.433Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 11:29:10,957 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\router.js change
2025-08-01 11:29:13,648 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 11:53:53,132 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1754020433128.8086,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-08-01T03:53:53.129Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 11:53:53,139 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1754020433136.0647,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-08-01T03:53:53.136Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 11:54:36,388 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1754020476385.9229,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-08-01T03:54:36.386Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 11:55:02,279 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1754020502270.8628,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-08-01T03:55:02.271Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 11:55:02,280 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1754020502274.6904,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-08-01T03:55:02.275Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 11:55:02,285 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1754020502280.0942,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-08-01T03:55:02.280Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 12:55:05,392 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1754024105382.8606,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-08-01T04:55:05.383Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 12:55:05,393 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1754024105382.8606,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-08-01T04:55:05.383Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 12:55:05,396 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1754024105387.8628,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-08-01T04:55:05.388Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 12:55:05,397 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1754024105392.8525,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-08-01T04:55:05.393Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 13:55:05,790 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1754027705781.4377,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-08-01T05:55:05.781Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 13:55:05,794 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1754027705781.4377,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-08-01T05:55:05.781Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 13:55:05,794 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1754027705781.4377,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-08-01T05:55:05.781Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 13:55:05,795 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1754027705788.0645,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-08-01T05:55:05.788Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 14:55:06,210 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1754031306203.032,"mtimeMs":1753847766871.636,"ctimeMs":1753847766871.636,"birthtimeMs":1753845075863.1326,"atime":"2025-08-01T06:55:06.203Z","mtime":"2025-07-30T03:56:06.872Z","ctime":"2025-07-30T03:56:06.872Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 14:55:06,210 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1754031306205.0857,"mtimeMs":1753848015842.23,"ctimeMs":1753848015842.23,"birthtimeMs":1753845075865.1868,"atime":"2025-08-01T06:55:06.205Z","mtime":"2025-07-30T04:00:15.842Z","ctime":"2025-07-30T04:00:15.842Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 14:55:06,214 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":6755399441436609,"size":0,"blocks":0,"atimeMs":1754031306207.158,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075891.5354,"atime":"2025-08-01T06:55:06.207Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.892Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 14:55:06,214 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10414574138675144,"size":0,"blocks":0,"atimeMs":1754031306208.1958,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075899.0144,"atime":"2025-08-01T06:55:06.208Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.899Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 15:07:22,164 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\service","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5348024557883333,"size":0,"blocks":0,"atimeMs":1754032042160.8325,"mtimeMs":1732008650000,"ctimeMs":1753845075924.5127,"birthtimeMs":1753845075894.4998,"atime":"2025-08-01T07:07:22.161Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.925Z","birthtime":"2025-07-30T03:11:15.894Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 15:07:22,170 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\public","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":11540474045517728,"size":0,"blocks":0,"atimeMs":1754032042165.833,"mtimeMs":1753845249891.6353,"ctimeMs":1753845249891.6353,"birthtimeMs":1753845075868.3948,"atime":"2025-08-01T07:07:22.166Z","mtime":"2025-07-30T03:14:09.892Z","ctime":"2025-07-30T03:14:09.892Z","birthtime":"2025-07-30T03:11:15.868Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 15:20:41,784 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\model\citys.js change
2025-08-01 15:20:41,532 INFO 5592 [egg-watcher] Received a change event from eventSource: {"path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app.js","event":"change","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":18014398509862772,"size":595,"blocks":1,"atimeMs":1754032841528.5461,"mtimeMs":1753845216497.8264,"ctimeMs":1753845216497.8264,"birthtimeMs":1753845075856.6013,"atime":"2025-08-01T07:20:41.529Z","mtime":"2025-07-30T03:13:36.498Z","ctime":"2025-07-30T03:13:36.498Z","birthtime":"2025-07-30T03:11:15.857Z"}}
2025-08-01 15:20:41,535 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\plugin.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700561,"size":389,"blocks":0,"atimeMs":1754032841528.5461,"mtimeMs":1753845180012.2527,"ctimeMs":1753845180012.2527,"birthtimeMs":1753845075908.4712,"atime":"2025-08-01T07:20:41.529Z","mtime":"2025-07-30T03:13:00.012Z","ctime":"2025-07-30T03:13:00.012Z","birthtime":"2025-07-30T03:11:15.908Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:41,535 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\config\\config.default.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":8444249301700557,"size":1576,"blocks":8,"atimeMs":1754032841528.5461,"mtimeMs":1753860473756.1506,"ctimeMs":1753860473756.1506,"birthtimeMs":1753845075904.9722,"atime":"2025-08-01T07:20:41.529Z","mtime":"2025-07-30T07:27:53.756Z","ctime":"2025-07-30T07:27:53.756Z","birthtime":"2025-07-30T03:11:15.905Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:41,566 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\utils\\tool.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":14636698789334984,"size":2765,"blocks":8,"atimeMs":1754032841546.7063,"mtimeMs":1732008650000,"ctimeMs":1753845075901.432,"birthtimeMs":1753845075900.4263,"atime":"2025-08-01T07:20:41.547Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.901Z","birthtime":"2025-07-30T03:11:15.900Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:41,566 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\schedule\\sendMessage.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697619396,"size":350,"blocks":0,"atimeMs":1754032841546.7063,"mtimeMs":1732008650000,"ctimeMs":1753845075893.994,"birthtimeMs":1753845075892.9944,"atime":"2025-08-01T07:20:41.547Z","mtime":"2024-11-19T09:30:50.000Z","ctime":"2025-07-30T03:11:15.894Z","birthtime":"2025-07-30T03:11:15.893Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:41,566 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1015,"blocks":8,"atimeMs":1754032841546.7063,"mtimeMs":1754011560432.5923,"ctimeMs":1754011560432.5923,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T07:20:41.547Z","mtime":"2025-08-01T01:26:00.433Z","ctime":"2025-08-01T01:26:00.433Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:41,572 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5910974511309155,"size":2797,"blocks":8,"atimeMs":1754032841546.7063,"mtimeMs":1753933417322.3918,"ctimeMs":1753933417322.3918,"birthtimeMs":1753847118676.5964,"atime":"2025-08-01T07:20:41.547Z","mtime":"2025-07-31T03:43:37.322Z","ctime":"2025-07-31T03:43:37.322Z","birthtime":"2025-07-30T03:45:18.677Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:41,573 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":3659174697623924,"size":4551,"blocks":16,"atimeMs":1754032841546.7063,"mtimeMs":1754017845796.2656,"ctimeMs":1754017845796.2656,"birthtimeMs":1753847132395.9258,"atime":"2025-08-01T07:20:41.547Z","mtime":"2025-08-01T03:10:45.796Z","ctime":"2025-08-01T03:10:45.796Z","birthtime":"2025-07-30T03:45:32.396Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:41,573 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\user.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861084092,"size":910,"blocks":8,"atimeMs":1754032841546.7063,"mtimeMs":1753848023924.8098,"ctimeMs":1753848023924.8098,"birthtimeMs":1753848013093.8396,"atime":"2025-08-01T07:20:41.547Z","mtime":"2025-07-30T04:00:23.925Z","ctime":"2025-07-30T04:00:23.925Z","birthtime":"2025-07-30T04:00:13.094Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:41,573 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":5066549581177188,"size":3606,"blocks":8,"atimeMs":1754032841546.7063,"mtimeMs":1753933425539.6057,"ctimeMs":1753933425539.6057,"birthtimeMs":1753847102917.4177,"atime":"2025-08-01T07:20:41.547Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-07-31T03:43:45.540Z","birthtime":"2025-07-30T03:45:02.917Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:41,573 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4222124651529413,"size":1035,"blocks":8,"atimeMs":1754032841546.7063,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1753847979734.9976,"atime":"2025-08-01T07:20:41.547Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-07-30T03:59:39.735Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:41,574 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\citys.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":7881299348279197,"size":997,"blocks":8,"atimeMs":1754032841546.7063,"mtimeMs":1753848028559.5122,"ctimeMs":1753848028559.5122,"birthtimeMs":1753845075867.3916,"atime":"2025-08-01T07:20:41.547Z","mtime":"2025-07-30T04:00:28.560Z","ctime":"2025-07-30T04:00:28.560Z","birthtime":"2025-07-30T03:11:15.867Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:20:43,906 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 15:26:04,821 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1754033164821.295,"mtimeMs":1754033164811.086,"ctimeMs":1754033164811.086,"birthtimeMs":1753845075865.1868,"atime":"2025-08-01T07:26:04.821Z","mtime":"2025-08-01T07:26:04.811Z","ctime":"2025-08-01T07:26:04.811Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 15:26:04,822 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"rename","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location copy.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":1035,"blocks":8,"atimeMs":1754033164820.2944,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:26:04.820Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:26:04,823 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location copy.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":1035,"blocks":8,"atimeMs":1754033164820.2944,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:26:04.820Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:26:04,823 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location copy.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":1035,"blocks":8,"atimeMs":1754033164820.2944,"mtimeMs":1753847981513.0913,"ctimeMs":1753847985211.0176,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:26:04.820Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-07-30T03:59:45.211Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:26:05,033 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\model\location copy.js change
2025-08-01 15:26:07,236 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\model\paths.js change
2025-08-01 15:26:06,968 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 15:26:07,030 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"rename","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\location copy.js","remove":true,"isDirectory":false,"isFile":false}
2025-08-01 15:26:07,031 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"rename","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":1035,"blocks":8,"atimeMs":1754033165712.629,"mtimeMs":1753847981513.0913,"ctimeMs":1754033167023.493,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:26:05.713Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-08-01T07:26:07.023Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:26:07,031 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":4503599627751323,"size":0,"blocks":0,"atimeMs":1754033167030.9136,"mtimeMs":1754033167023.493,"ctimeMs":1754033167023.493,"birthtimeMs":1753845075865.1868,"atime":"2025-08-01T07:26:07.031Z","mtime":"2025-08-01T07:26:07.023Z","ctime":"2025-08-01T07:26:07.023Z","birthtime":"2025-07-30T03:11:15.865Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 15:26:07,031 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":1035,"blocks":8,"atimeMs":1754033165712.629,"mtimeMs":1753847981513.0913,"ctimeMs":1754033167023.493,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:26:05.713Z","mtime":"2025-07-30T03:59:41.513Z","ctime":"2025-08-01T07:26:07.023Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:08,265 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\model\paths.js change
2025-08-01 15:27:08,050 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":962,"blocks":8,"atimeMs":1754033228034.5469,"mtimeMs":1754033228034.5469,"ctimeMs":1754033228034.5469,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:27:08.035Z","mtime":"2025-08-01T07:27:08.035Z","ctime":"2025-08-01T07:27:08.035Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:08,051 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":962,"blocks":8,"atimeMs":1754033228034.5469,"mtimeMs":1754033228034.5469,"ctimeMs":1754033228034.5469,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:27:08.035Z","mtime":"2025-08-01T07:27:08.035Z","ctime":"2025-08-01T07:27:08.035Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:08,051 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":962,"blocks":8,"atimeMs":1754033228034.5469,"mtimeMs":1754033228034.5469,"ctimeMs":1754033228034.5469,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:27:08.035Z","mtime":"2025-08-01T07:27:08.035Z","ctime":"2025-08-01T07:27:08.035Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:10,116 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 15:27:25,359 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\model\paths.js change
2025-08-01 15:27:25,146 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":959,"blocks":8,"atimeMs":1754033245128.91,"mtimeMs":1754033245128.91,"ctimeMs":1754033245128.91,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:27:25.129Z","mtime":"2025-08-01T07:27:25.129Z","ctime":"2025-08-01T07:27:25.129Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:25,147 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":959,"blocks":8,"atimeMs":1754033245128.91,"mtimeMs":1754033245128.91,"ctimeMs":1754033245128.91,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:27:25.129Z","mtime":"2025-08-01T07:27:25.129Z","ctime":"2025-08-01T07:27:25.129Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:25,147 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\model\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":17732923533662780,"size":959,"blocks":8,"atimeMs":1754033245128.91,"mtimeMs":1754033245128.91,"ctimeMs":1754033245128.91,"birthtimeMs":1754033164811.086,"atime":"2025-08-01T07:27:25.129Z","mtime":"2025-08-01T07:27:25.129Z","ctime":"2025-08-01T07:27:25.129Z","birthtime":"2025-08-01T07:26:04.811Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:48,302 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys copy.js change
2025-08-01 15:27:48,091 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"rename","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys copy.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033268086.7812,"mtimeMs":1753933425539.6057,"ctimeMs":1753933425539.6057,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:27:48.087Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-07-31T03:43:45.540Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:48,092 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1754033268090.8647,"mtimeMs":1754033268082.4346,"ctimeMs":1754033268082.4346,"birthtimeMs":1753845075863.1326,"atime":"2025-08-01T07:27:48.091Z","mtime":"2025-08-01T07:27:48.082Z","ctime":"2025-08-01T07:27:48.082Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 15:27:48,092 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys copy.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033268086.7812,"mtimeMs":1753933425539.6057,"ctimeMs":1753933425539.6057,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:27:48.087Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-07-31T03:43:45.540Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:48,092 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys copy.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033268086.7812,"mtimeMs":1753933425539.6057,"ctimeMs":1753933425539.6057,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:27:48.087Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-07-31T03:43:45.540Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:50,327 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\paths.js change
2025-08-01 15:27:50,118 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"rename","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\citys copy.js","remove":true,"isDirectory":false,"isFile":false}
2025-08-01 15:27:50,118 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"rename","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033268695.2441,"mtimeMs":1753933425539.6057,"ctimeMs":1754033270109.4358,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:27:48.695Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-08-01T07:27:50.109Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:50,118 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller","stat":{"dev":1279778744,"mode":16822,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":13792273859202944,"size":0,"blocks":0,"atimeMs":1754033270117.6726,"mtimeMs":1754033270109.4358,"ctimeMs":1754033270109.4358,"birthtimeMs":1753845075863.1326,"atime":"2025-08-01T07:27:50.118Z","mtime":"2025-08-01T07:27:50.109Z","ctime":"2025-08-01T07:27:50.109Z","birthtime":"2025-07-30T03:11:15.863Z"},"remove":false,"isDirectory":true,"isFile":false}
2025-08-01 15:27:50,118 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033268695.2441,"mtimeMs":1753933425539.6057,"ctimeMs":1754033270109.4358,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:27:48.695Z","mtime":"2025-07-31T03:43:45.540Z","ctime":"2025-08-01T07:27:50.109Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:27:50,437 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 15:27:50,489 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 15:29:03,342 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\paths.js change
2025-08-01 15:29:03,133 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033343119.6157,"mtimeMs":1754033343119.6157,"ctimeMs":1754033343119.6157,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:03.120Z","mtime":"2025-08-01T07:29:03.120Z","ctime":"2025-08-01T07:29:03.120Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:03,134 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033343119.6157,"mtimeMs":1754033343119.6157,"ctimeMs":1754033343119.6157,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:03.120Z","mtime":"2025-08-01T07:29:03.120Z","ctime":"2025-08-01T07:29:03.120Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:03,134 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033343119.6157,"mtimeMs":1754033343119.6157,"ctimeMs":1754033343119.6157,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:03.120Z","mtime":"2025-08-01T07:29:03.120Z","ctime":"2025-08-01T07:29:03.120Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,047 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344035.3147,"mtimeMs":1754033344035.3147,"ctimeMs":1754033344035.3147,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.035Z","mtime":"2025-08-01T07:29:04.035Z","ctime":"2025-08-01T07:29:04.035Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,047 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344035.3147,"mtimeMs":1754033344035.3147,"ctimeMs":1754033344035.3147,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.035Z","mtime":"2025-08-01T07:29:04.035Z","ctime":"2025-08-01T07:29:04.035Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,048 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344035.3147,"mtimeMs":1754033344035.3147,"ctimeMs":1754033344035.3147,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.035Z","mtime":"2025-08-01T07:29:04.035Z","ctime":"2025-08-01T07:29:04.035Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,188 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344175.868,"mtimeMs":1754033344175.868,"ctimeMs":1754033344175.868,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.176Z","mtime":"2025-08-01T07:29:04.176Z","ctime":"2025-08-01T07:29:04.176Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,188 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344175.868,"mtimeMs":1754033344175.868,"ctimeMs":1754033344175.868,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.176Z","mtime":"2025-08-01T07:29:04.176Z","ctime":"2025-08-01T07:29:04.176Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,189 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344175.868,"mtimeMs":1754033344175.868,"ctimeMs":1754033344175.868,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.176Z","mtime":"2025-08-01T07:29:04.176Z","ctime":"2025-08-01T07:29:04.176Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,345 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344330.4397,"mtimeMs":1754033344330.4397,"ctimeMs":1754033344330.4397,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.330Z","mtime":"2025-08-01T07:29:04.330Z","ctime":"2025-08-01T07:29:04.330Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,345 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344330.4397,"mtimeMs":1754033344330.4397,"ctimeMs":1754033344330.4397,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.330Z","mtime":"2025-08-01T07:29:04.330Z","ctime":"2025-08-01T07:29:04.330Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,345 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344330.4397,"mtimeMs":1754033344330.4397,"ctimeMs":1754033344330.4397,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.330Z","mtime":"2025-08-01T07:29:04.330Z","ctime":"2025-08-01T07:29:04.330Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,518 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344505.3098,"mtimeMs":1754033344505.3098,"ctimeMs":1754033344505.3098,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.505Z","mtime":"2025-08-01T07:29:04.505Z","ctime":"2025-08-01T07:29:04.505Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,518 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344505.3098,"mtimeMs":1754033344505.3098,"ctimeMs":1754033344505.3098,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.505Z","mtime":"2025-08-01T07:29:04.505Z","ctime":"2025-08-01T07:29:04.505Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,519 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033344505.3098,"mtimeMs":1754033344505.3098,"ctimeMs":1754033344505.3098,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:04.505Z","mtime":"2025-08-01T07:29:04.505Z","ctime":"2025-08-01T07:29:04.505Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:04,729 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\paths.js change
2025-08-01 15:29:06,486 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 15:29:06,561 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 15:29:29,591 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\router.js change
2025-08-01 15:29:29,380 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1202,"blocks":8,"atimeMs":1754033369372.193,"mtimeMs":1754033369372.193,"ctimeMs":1754033369372.193,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T07:29:29.372Z","mtime":"2025-08-01T07:29:29.372Z","ctime":"2025-08-01T07:29:29.372Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:29,380 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1202,"blocks":8,"atimeMs":1754033369372.193,"mtimeMs":1754033369372.193,"ctimeMs":1754033369372.193,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T07:29:29.372Z","mtime":"2025-08-01T07:29:29.372Z","ctime":"2025-08-01T07:29:29.372Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:29,381 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1202,"blocks":8,"atimeMs":1754033369372.193,"mtimeMs":1754033369372.193,"ctimeMs":1754033369372.193,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T07:29:29.372Z","mtime":"2025-08-01T07:29:29.372Z","ctime":"2025-08-01T07:29:29.372Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:31,654 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 15:29:40,696 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\controller\paths.js change
2025-08-01 15:29:40,489 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033380472.5723,"mtimeMs":1754033380472.5723,"ctimeMs":1754033380472.5723,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:40.473Z","mtime":"2025-08-01T07:29:40.473Z","ctime":"2025-08-01T07:29:40.473Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:40,489 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033380472.5723,"mtimeMs":1754033380472.5723,"ctimeMs":1754033380472.5723,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:40.473Z","mtime":"2025-08-01T07:29:40.473Z","ctime":"2025-08-01T07:29:40.473Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:40,489 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\controller\\paths.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":1688849861157620,"size":3606,"blocks":8,"atimeMs":1754033380472.5723,"mtimeMs":1754033380472.5723,"ctimeMs":1754033380472.5723,"birthtimeMs":1754033268082.4346,"atime":"2025-08-01T07:29:40.473Z","mtime":"2025-08-01T07:29:40.473Z","ctime":"2025-08-01T07:29:40.473Z","birthtime":"2025-08-01T07:27:48.082Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:42,737 WARN 5592 [agent:development] reload worker because C:\Users\<USER>\Desktop\plan-map\后端\app\router.js change
2025-08-01 15:29:42,526 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1202,"blocks":8,"atimeMs":1754033382511.5542,"mtimeMs":1754033382511.5542,"ctimeMs":1754033382511.5542,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T07:29:42.512Z","mtime":"2025-08-01T07:29:42.512Z","ctime":"2025-08-01T07:29:42.512Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:42,526 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1202,"blocks":8,"atimeMs":1754033382511.5542,"mtimeMs":1754033382511.5542,"ctimeMs":1754033382511.5542,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T07:29:42.512Z","mtime":"2025-08-01T07:29:42.512Z","ctime":"2025-08-01T07:29:42.512Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:42,526 INFO 5592 [egg-watcher] Received a change event from eventSource: {"event":"change","path":"C:\\Users\\<USER>\\Desktop\\plan-map\\后端\\app\\router.js","stat":{"dev":1279778744,"mode":33206,"nlink":1,"uid":0,"gid":0,"rdev":0,"blksize":4096,"ino":10696049115385792,"size":1202,"blocks":8,"atimeMs":1754033382511.5542,"mtimeMs":1754033382511.5542,"ctimeMs":1754033382511.5542,"birthtimeMs":1753845075890.531,"atime":"2025-08-01T07:29:42.512Z","mtime":"2025-08-01T07:29:42.512Z","ctime":"2025-08-01T07:29:42.512Z","birthtime":"2025-07-30T03:11:15.891Z"},"remove":false,"isDirectory":false,"isFile":true}
2025-08-01 15:29:42,831 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
2025-08-01 15:29:42,877 WARN 5592 [ClusterClient:Connection] socket is closed by other side while there were still unhandled data in the socket buffer
