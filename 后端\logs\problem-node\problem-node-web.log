2025-07-30 11:51:05,099 INFO 2348 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:22:05,628 INFO 27972 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:24:34,457 INFO 27136 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:27:16,564 INFO 27136 [-/127.0.0.1/-/19ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:27:16,565 INFO 27136 [-/127.0.0.1/-/20ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:27:24,821 INFO 27136 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:27:24,822 INFO 27136 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:27:48,254 INFO 17724 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:27:49,444 INFO 17724 [-/127.0.0.1/-/35ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:27:49,446 INFO 17724 [-/127.0.0.1/-/37ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:27:51,608 INFO 17724 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:27:51,611 INFO 17724 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:27:56,252 INFO 21228 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:28:30,391 INFO 21228 [-/127.0.0.1/-/48ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:28:30,394 INFO 21228 [-/127.0.0.1/-/51ms POST /citys/list] [egg-sequelize](6ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:28:48,304 INFO 21228 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`id` = 1;
2025-07-30 15:28:48,307 INFO 21228 [-/127.0.0.1/-/18ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`id` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:28:51,726 INFO 21228 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`id` = 10;
2025-07-30 15:28:51,727 INFO 21228 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`id` = 10 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:29:01,590 INFO 21228 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`id` = 1;
2025-07-30 15:29:01,591 INFO 21228 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`id` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:29:10,029 INFO 21228 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`city` = '北京市';
2025-07-30 15:29:10,030 INFO 21228 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`city` = '北京市' ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:29:12,405 INFO 21228 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`city` = '北京1市';
2025-07-30 15:29:12,406 INFO 21228 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`city` = '北京1市' ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:29:36,856 INFO 30196 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:31:30,610 INFO 19412 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-07-30 15:46:15,319 INFO 19412 [-/127.0.0.1/-/43ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:46:15,322 INFO 19412 [-/127.0.0.1/-/46ms POST /citys/list] [egg-sequelize](6ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:46:44,507 INFO 19412 [-/127.0.0.1/-/23ms POST /citys/list] [egg-sequelize](6ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:46:44,509 INFO 19412 [-/127.0.0.1/-/25ms POST /citys/list] [egg-sequelize](7ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:46:51,242 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:46:51,244 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:48:37,006 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:48:37,007 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:49:09,553 INFO 19412 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:49:09,553 INFO 19412 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:49:09,569 INFO 19412 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:49:09,571 INFO 19412 [-/127.0.0.1/-/8ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:49:12,118 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:49:12,120 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:49:12,209 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:49:12,211 INFO 19412 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:49:57,124 INFO 19412 [-/127.0.0.1/-/22ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:49:57,126 INFO 19412 [-/127.0.0.1/-/24ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:49:57,225 INFO 19412 [-/127.0.0.1/-/8ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:49:57,227 INFO 19412 [-/127.0.0.1/-/11ms POST /location/list] [egg-sequelize](6ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:50:12,333 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:50:12,334 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:50:12,345 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:50:12,348 INFO 19412 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:50:16,588 INFO 19412 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:50:16,589 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:50:16,694 INFO 19412 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:50:16,695 INFO 19412 [-/127.0.0.1/-/8ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:50:19,985 INFO 19412 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:50:19,986 INFO 19412 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:50:20,025 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:50:20,025 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:50:31,410 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:50:31,412 INFO 19412 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:50:31,424 INFO 19412 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:50:31,425 INFO 19412 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:50:33,740 INFO 19412 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:50:33,742 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:50:33,827 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:50:33,828 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:51:13,834 INFO 19412 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:51:13,835 INFO 19412 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:51:13,848 INFO 19412 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:51:13,849 INFO 19412 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:51:16,953 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](6ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:51:16,955 INFO 19412 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](7ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:51:17,089 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:51:17,090 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:52:51,199 INFO 19412 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:52:51,200 INFO 19412 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:52:51,218 INFO 19412 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:52:51,220 INFO 19412 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:53:37,343 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:53:37,344 INFO 19412 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:53:37,359 INFO 19412 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:53:37,360 INFO 19412 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:53:44,674 INFO 19412 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:53:44,675 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:53:44,705 INFO 19412 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:53:44,706 INFO 19412 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:55:12,149 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:55:12,150 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:55:12,163 INFO 19412 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:55:12,164 INFO 19412 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:55:29,882 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:55:29,883 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:55:29,893 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:55:29,894 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:55:37,636 INFO 19412 [-/127.0.0.1/-/65ms POST /citys/list] [egg-sequelize](49ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:55:37,674 INFO 19412 [-/127.0.0.1/-/103ms POST /citys/list] [egg-sequelize](99ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:55:37,751 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:55:37,752 INFO 19412 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:56:12,529 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:56:12,531 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:56:12,540 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:56:12,540 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:56:34,203 INFO 19412 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:56:34,203 INFO 19412 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:56:34,261 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:56:34,262 INFO 19412 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:56:45,562 INFO 19412 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:56:45,564 INFO 19412 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:56:45,639 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:56:45,641 INFO 19412 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:57:07,889 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:57:07,889 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:57:07,899 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:57:07,900 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:57:46,863 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:57:46,864 INFO 19412 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:57:46,877 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:57:46,878 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:57:52,001 INFO 19412 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:57:52,001 INFO 19412 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:57:52,009 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:57:52,010 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:57:54,468 INFO 19412 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:57:54,469 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:57:54,566 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:57:54,567 INFO 19412 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 15:58:53,832 INFO 19412 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 15:58:53,834 INFO 19412 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 15:58:53,932 INFO 19412 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 15:58:53,933 INFO 19412 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:00:36,942 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:00:36,942 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:00:36,953 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:00:36,953 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:00:56,834 INFO 19412 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:00:56,835 INFO 19412 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:00:56,942 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:00:56,943 INFO 19412 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:01:38,018 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:01:38,019 INFO 19412 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:01:38,029 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:01:38,029 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:01:42,680 INFO 19412 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:01:42,680 INFO 19412 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:01:42,723 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:01:42,724 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:02:06,612 INFO 19412 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:02:06,613 INFO 19412 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:02:06,692 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:02:06,693 INFO 19412 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:04:00,133 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:04:00,134 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:04:00,142 INFO 19412 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:04:00,143 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:05:33,033 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:05:33,033 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:05:33,042 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:05:33,043 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:06:05,904 INFO 19412 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:06:05,904 INFO 19412 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:06:05,916 INFO 19412 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:06:05,917 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:06:37,341 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:06:37,341 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:06:37,350 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:06:37,351 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:06:50,353 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:06:50,354 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:06:50,366 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:06:50,366 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:06:57,619 INFO 19412 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:06:57,620 INFO 19412 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:06:57,629 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:06:57,629 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:07:15,570 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:07:15,571 INFO 19412 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:07:15,581 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:07:15,582 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:07:31,805 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:07:31,806 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:07:31,814 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:07:31,814 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:07:54,318 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:07:54,318 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:07:54,330 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:07:54,332 INFO 19412 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:08:44,515 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:08:44,516 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:08:44,524 INFO 19412 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:08:44,524 INFO 19412 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:09:04,407 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:09:04,408 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:09:04,417 INFO 19412 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:09:04,418 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:09:10,421 INFO 19412 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:09:10,422 INFO 19412 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:09:10,473 INFO 19412 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:09:10,475 INFO 19412 [-/127.0.0.1/-/8ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:09:14,668 INFO 19412 [-/127.0.0.1/-/4ms POST /citys/delete] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`id` = 1;
2025-07-30 16:09:14,683 INFO 19412 [-/127.0.0.1/-/19ms POST /citys/delete] [egg-sequelize](13ms) Executed (default): DELETE FROM `citys` WHERE `id` = 1
2025-07-30 16:09:18,471 INFO 19412 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:09:18,472 INFO 19412 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:09:18,564 INFO 19412 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:09:18,565 INFO 19412 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:10:08,937 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:10:08,937 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:10:08,946 INFO 19412 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:10:08,947 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:10:44,743 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:10:44,744 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:10:44,752 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:10:44,752 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:11:08,810 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:11:08,811 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:11:08,819 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:11:08,820 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:12:33,262 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:12:33,263 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:12:33,275 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:12:33,275 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:12:39,407 INFO 19412 [-/127.0.0.1/-/12ms POST /location/save] [egg-sequelize](6ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`) VALUES (DEFAULT,?,?,?,?,?,?);
2025-07-30 16:12:42,753 INFO 19412 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:12:42,754 INFO 19412 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:12:42,802 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:12:42,803 INFO 19412 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:12:49,792 INFO 19412 [-/127.0.0.1/-/2ms POST /location/save] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 6;
2025-07-30 16:12:49,808 INFO 19412 [-/127.0.0.1/-/18ms POST /location/save] [egg-sequelize](14ms) Executed (default): UPDATE `location` SET `name`=? WHERE `id` = ?
2025-07-30 16:13:22,430 INFO 19412 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:13:22,432 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:13:22,441 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:13:22,442 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:13:41,166 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:13:41,166 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:13:41,177 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:13:41,178 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:13:44,456 INFO 19412 [-/127.0.0.1/-/2ms POST /location/delete] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 6;
2025-07-30 16:13:44,470 INFO 19412 [-/127.0.0.1/-/16ms POST /location/delete] [egg-sequelize](13ms) Executed (default): DELETE FROM `location` WHERE `id` = 6
2025-07-30 16:14:16,293 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:14:16,293 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:14:16,303 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:14:16,303 INFO 19412 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:14:27,616 INFO 19412 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:14:27,617 INFO 19412 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:14:27,625 INFO 19412 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:14:27,626 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:14:30,218 INFO 19412 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:14:30,218 INFO 19412 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:14:30,226 INFO 19412 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:14:30,227 INFO 19412 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:16:20,207 INFO 2276 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:16:55,618 INFO 2276 [-/127.0.0.1/-/38ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:16:55,620 INFO 2276 [-/127.0.0.1/-/40ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:16:55,636 INFO 2276 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:16:55,637 INFO 2276 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:18:48,963 INFO 14128 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:18:55,086 INFO 35460 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:21:16,639 INFO 3504 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:21:20,242 INFO 3504 [-/127.0.0.1/-/48ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:21:20,247 INFO 3504 [-/127.0.0.1/-/53ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:21:20,297 INFO 3504 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:21:20,303 INFO 3504 [-/127.0.0.1/-/13ms POST /location/list] [egg-sequelize](7ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:23:42,341 INFO 3504 [-/127.0.0.1/-/18ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:23:42,342 INFO 3504 [-/127.0.0.1/-/19ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:23:42,371 INFO 3504 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:23:42,373 INFO 3504 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:25:07,268 INFO 28620 [egg-sequelize](2ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:25:26,484 INFO 29896 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:25:39,562 INFO 15172 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:25:54,512 INFO 21672 [egg-sequelize](2ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:26:10,205 INFO 28544 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:26:19,367 INFO 19332 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:26:43,644 INFO 35504 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:26:53,579 INFO 35784 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:27:09,159 INFO 12452 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:27:18,053 INFO 36896 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:27:50,094 INFO 38684 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:27:57,949 INFO 30144 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:28:17,286 INFO 20320 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:28:26,881 INFO 21076 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:28:32,346 INFO 4208 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:29:20,366 INFO 4208 [-/127.0.0.1/-/19ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:29:20,369 INFO 4208 [-/127.0.0.1/-/22ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:29:20,383 INFO 4208 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:29:20,384 INFO 4208 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:30:13,387 INFO 4208 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:30:13,388 INFO 4208 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:30:13,399 INFO 4208 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:30:13,400 INFO 4208 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:31:31,116 INFO 4208 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:31:31,117 INFO 4208 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:31:31,128 INFO 4208 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:31:31,129 INFO 4208 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:31:34,138 INFO 4208 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:31:34,139 INFO 4208 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:31:34,220 INFO 4208 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:31:34,221 INFO 4208 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:31:48,793 INFO 4208 [-/127.0.0.1/-/20ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:31:48,794 INFO 4208 [-/127.0.0.1/-/21ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:31:48,852 INFO 4208 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:31:48,854 INFO 4208 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:36:15,630 INFO 4208 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:36:15,631 INFO 4208 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:36:15,663 INFO 4208 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:36:15,664 INFO 4208 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:36:51,152 INFO 4208 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:36:51,156 INFO 4208 [-/127.0.0.1/-/18ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:36:51,202 INFO 4208 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:36:51,203 INFO 4208 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:38:31,661 INFO 4208 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:38:31,662 INFO 4208 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:38:31,675 INFO 4208 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:38:31,675 INFO 4208 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:39:11,559 INFO 4208 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:39:11,560 INFO 4208 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:39:11,592 INFO 4208 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:39:11,593 INFO 4208 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:40:00,394 INFO 27532 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:40:08,018 INFO 27532 [-/127.0.0.1/-/49ms POST /citys/list] [egg-sequelize](6ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:40:08,023 INFO 27532 [-/127.0.0.1/-/54ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:40:08,075 INFO 27532 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:40:08,078 INFO 27532 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:42:37,274 INFO 27532 [-/127.0.0.1/-/17ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:42:37,275 INFO 27532 [-/127.0.0.1/-/18ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:42:37,335 INFO 27532 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:42:37,336 INFO 27532 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:44:51,922 INFO 27532 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:44:51,925 INFO 27532 [-/127.0.0.1/-/16ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:44:51,958 INFO 27532 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:44:51,959 INFO 27532 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:45:55,724 INFO 27532 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:45:55,725 INFO 27532 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:45:55,738 INFO 27532 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:45:55,739 INFO 27532 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:49:12,683 INFO 27532 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:49:12,684 INFO 27532 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:49:12,693 INFO 27532 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:49:12,693 INFO 27532 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:49:42,290 INFO 35672 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-30 16:50:25,631 INFO 35672 [-/127.0.0.1/-/35ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:50:25,634 INFO 35672 [-/127.0.0.1/-/38ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:50:25,649 INFO 35672 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:50:25,651 INFO 35672 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:50:28,840 INFO 35672 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-30 16:50:28,841 INFO 35672 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-30 16:50:28,852 INFO 35672 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-30 16:50:28,853 INFO 35672 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-30 16:50:37,306 INFO 35672 [-/127.0.0.1/-/12ms POST /citys/save] [egg-sequelize](4ms) Executed (default): INSERT INTO `citys` (`id`,`province`,`city`,`district`,`lng`,`lat`,`time`) VALUES (DEFAULT,?,?,?,?,?,?);
2025-07-30 17:26:24,746 INFO 29020 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 09:23:59,956 INFO 4688 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-07-31 09:24:03,432 INFO 28900 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 09:24:09,906 INFO 28900 [-/127.0.0.1/-/37ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:24:09,910 INFO 28900 [-/127.0.0.1/-/41ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:24:09,945 INFO 28900 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:24:09,946 INFO 28900 [-/127.0.0.1/-/8ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:24:20,299 INFO 28900 [-/127.0.0.1/-/12ms POST /citys/setdefault] [egg-sequelize](6ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:24:20,304 INFO 28900 [-/127.0.0.1/-/17ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:24:42,332 INFO 28900 [-/127.0.0.1/-/12ms POST /citys/setdefault] [egg-sequelize](6ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:24:42,337 INFO 28900 [-/127.0.0.1/-/17ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:25:04,724 INFO 28900 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:25:04,725 INFO 28900 [-/127.0.0.1/-/16ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:25:04,739 INFO 28900 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:25:04,740 INFO 28900 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:25:08,375 INFO 28900 [-/127.0.0.1/-/5ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:25:08,380 INFO 28900 [-/127.0.0.1/-/10ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:25:11,028 INFO 28900 [-/127.0.0.1/-/7ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:25:11,033 INFO 28900 [-/127.0.0.1/-/12ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:25:14,471 INFO 28900 [-/127.0.0.1/-/5ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:25:14,476 INFO 28900 [-/127.0.0.1/-/10ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:25:30,637 INFO 28900 [-/127.0.0.1/-/12ms POST /citys/save] [egg-sequelize](5ms) Executed (default): INSERT INTO `citys` (`id`,`province`,`city`,`district`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 09:29:57,732 INFO 28900 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:29:57,734 INFO 28900 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:29:57,857 INFO 28900 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:29:57,859 INFO 28900 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:29:59,388 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:29:59,389 INFO 28900 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:29:59,417 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:29:59,418 INFO 28900 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:30:07,919 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-31 09:30:07,920 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:30:07,928 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-31 09:30:07,929 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:30:27,881 INFO 28900 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:30:27,884 INFO 28900 [-/127.0.0.1/-/17ms POST /citys/list] [egg-sequelize](7ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-31 09:30:27,920 INFO 28900 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-31 09:30:27,922 INFO 28900 [-/127.0.0.1/-/8ms POST /location/list] [egg-sequelize](6ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:30:48,736 INFO 28900 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-31 09:30:48,737 INFO 28900 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:30:48,750 INFO 28900 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-31 09:30:48,751 INFO 28900 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:31:19,145 INFO 28900 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:31:19,146 INFO 28900 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:31:19,157 INFO 28900 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:31:19,158 INFO 28900 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:31:33,968 INFO 28900 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:31:33,970 INFO 28900 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:31:34,023 INFO 28900 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:31:34,024 INFO 28900 [-/127.0.0.1/-/8ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:31:37,814 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/delete] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`id` = 5;
2025-07-31 09:31:37,818 INFO 28900 [-/127.0.0.1/-/7ms POST /citys/delete] [egg-sequelize](3ms) Executed (default): DELETE FROM `citys` WHERE `id` = 5
2025-07-31 09:33:49,692 INFO 28900 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:33:49,693 INFO 28900 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:33:49,702 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:33:49,702 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:33:52,559 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:33:52,560 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:33:52,570 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:33:52,571 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:33:55,439 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/delete] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`id` = 3;
2025-07-31 09:33:55,442 INFO 28900 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:33:55,443 INFO 28900 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:33:55,445 INFO 28900 [-/127.0.0.1/-/9ms POST /citys/delete] [egg-sequelize](3ms) Executed (default): DELETE FROM `citys` WHERE `id` = 3
2025-07-31 09:33:55,451 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:33:55,453 INFO 28900 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:00,080 INFO 28900 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:34:00,081 INFO 28900 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:00,157 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:34:00,158 INFO 28900 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:04,641 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/delete] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`id` = 2;
2025-07-31 09:34:04,642 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:34:04,642 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:04,651 INFO 28900 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:34:04,652 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:04,653 INFO 28900 [-/127.0.0.1/-/15ms POST /citys/delete] [egg-sequelize](12ms) Executed (default): DELETE FROM `citys` WHERE `id` = 2
2025-07-31 09:34:23,424 INFO 28900 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:34:23,424 INFO 28900 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:23,461 INFO 28900 [-/127.0.0.1/-/13ms POST /location/list] [egg-sequelize](8ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:23,463 INFO 28900 [-/127.0.0.1/-/15ms POST /location/list] [egg-sequelize](11ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:34:40,811 INFO 28900 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:34:40,812 INFO 28900 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:40,820 INFO 28900 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:34:40,821 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:45,316 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/delete] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`id` = 6;
2025-07-31 09:34:45,331 INFO 28900 [-/127.0.0.1/-/16ms POST /citys/delete] [egg-sequelize](14ms) Executed (default): DELETE FROM `citys` WHERE `id` = 6
2025-07-31 09:34:45,339 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:34:45,340 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:45,349 INFO 28900 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:34:45,350 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:49,669 INFO 28900 [-/127.0.0.1/-/7ms POST /citys/save] [egg-sequelize](4ms) Executed (default): INSERT INTO `citys` (`id`,`province`,`city`,`district`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 09:34:55,523 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:34:55,524 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:55,533 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:34:55,534 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:58,153 INFO 28900 [-/127.0.0.1/-/1ms POST /citys/delete] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`id` = 4;
2025-07-31 09:34:58,156 INFO 28900 [-/127.0.0.1/-/5ms POST /citys/delete] [egg-sequelize](2ms) Executed (default): DELETE FROM `citys` WHERE `id` = 4
2025-07-31 09:34:58,165 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:34:58,165 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:34:58,177 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:34:58,178 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:35:01,905 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:35:01,906 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:35:01,981 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:35:01,982 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:35:09,880 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:35:09,881 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:35:09,889 INFO 28900 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:35:09,890 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:35:20,101 INFO 28900 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:35:20,102 INFO 28900 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:35:20,185 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:35:20,185 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:36:55,646 INFO 28900 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:36:55,647 INFO 28900 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:36:55,656 INFO 28900 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:36:55,657 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:02,988 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:37:02,989 INFO 28900 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:03,076 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:37:03,077 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:13,217 INFO 28900 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:37:13,218 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:13,226 INFO 28900 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:37:13,227 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:15,950 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:37:15,950 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:16,028 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:37:16,028 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:22,265 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:37:22,266 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:22,275 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:37:22,275 INFO 28900 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:24,052 INFO 28900 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:37:24,053 INFO 28900 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:24,066 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:37:24,067 INFO 28900 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:35,780 INFO 28900 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:37:35,781 INFO 28900 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:37:35,883 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:37:35,884 INFO 28900 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:40:49,642 INFO 28900 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:40:49,643 INFO 28900 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:40:49,654 INFO 28900 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:40:49,655 INFO 28900 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:41:14,662 INFO 28900 [-/127.0.0.1/-/9ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:41:14,667 INFO 28900 [-/127.0.0.1/-/14ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:41:18,777 INFO 28900 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:41:18,779 INFO 28900 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:41:18,802 INFO 28900 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:41:18,803 INFO 28900 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:46:34,870 INFO 30892 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 09:46:45,549 INFO 30892 [-/127.0.0.1/-/92ms POST /citys/list] [egg-sequelize](6ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:46:45,553 INFO 30892 [-/127.0.0.1/-/96ms POST /citys/list] [egg-sequelize](9ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:46:45,671 INFO 30892 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:46:45,673 INFO 30892 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:47:28,309 INFO 30892 [-/127.0.0.1/-/18ms POST /citys/save] [egg-sequelize](5ms) Executed (default): INSERT INTO `citys` (`id`,`province`,`city`,`district`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 09:51:13,920 INFO 30892 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:51:13,921 INFO 30892 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:51:13,933 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:51:13,934 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:51:30,507 INFO 30892 [-/127.0.0.1/-/6ms POST /citys/delete] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`id` = 7;
2025-07-31 09:51:30,512 INFO 30892 [-/127.0.0.1/-/11ms POST /citys/delete] [egg-sequelize](4ms) Executed (default): DELETE FROM `citys` WHERE `id` = 7
2025-07-31 09:51:30,522 INFO 30892 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:51:30,524 INFO 30892 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:51:30,536 INFO 30892 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:51:30,537 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:51:32,470 INFO 30892 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:51:32,471 INFO 30892 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:51:32,490 INFO 30892 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:51:32,491 INFO 30892 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:51:52,332 INFO 30892 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:51:52,333 INFO 30892 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:51:52,442 INFO 30892 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:51:52,444 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:52:07,569 INFO 30892 [-/127.0.0.1/-/18ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:52:07,570 INFO 30892 [-/127.0.0.1/-/19ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:52:07,629 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:52:07,630 INFO 30892 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:52:51,542 INFO 30892 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:52:51,542 INFO 30892 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:52:51,553 INFO 30892 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:52:51,554 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:53:47,872 INFO 30892 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:53:47,873 INFO 30892 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:53:47,883 INFO 30892 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:53:47,883 INFO 30892 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:55:31,058 INFO 30892 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:55:31,059 INFO 30892 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:55:31,071 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:55:31,071 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:55:32,459 INFO 30892 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:55:32,461 INFO 30892 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:55:32,481 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:55:32,494 INFO 30892 [-/127.0.0.1/-/17ms POST /location/list] [egg-sequelize](15ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:55:37,012 INFO 30892 [-/127.0.0.1/-/7ms POST /citys/save] [egg-sequelize](4ms) Executed (default): INSERT INTO `citys` (`id`,`province`,`city`,`district`,`lng`,`lat`,`active`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?,?);
2025-07-31 09:55:37,030 INFO 30892 [-/127.0.0.1/-/7ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:55:37,036 INFO 30892 [-/127.0.0.1/-/13ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:56:08,183 INFO 30892 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:56:08,183 INFO 30892 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:56:08,192 INFO 30892 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:56:08,193 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:56:17,475 INFO 30892 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:56:17,476 INFO 30892 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:56:17,546 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:56:17,547 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:56:34,763 INFO 30892 [-/127.0.0.1/-/17ms POST /citys/list] [egg-sequelize](7ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:56:34,764 INFO 30892 [-/127.0.0.1/-/18ms POST /citys/list] [egg-sequelize](7ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:56:34,855 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:56:34,856 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:56:39,653 INFO 30892 [-/127.0.0.1/-/5ms POST /citys/save] [egg-sequelize](4ms) Executed (default): INSERT INTO `citys` (`id`,`province`,`city`,`district`,`lng`,`lat`,`active`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?,?);
2025-07-31 09:56:39,670 INFO 30892 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:56:39,672 INFO 30892 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:56:39,673 INFO 30892 [-/127.0.0.1/-/8ms POST /citys/setdefault] [egg-sequelize](6ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:56:39,681 INFO 30892 [-/127.0.0.1/-/16ms POST /citys/setdefault] [egg-sequelize](5ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:56:39,685 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:56:39,686 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:00,372 INFO 30892 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:57:00,373 INFO 30892 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:00,384 INFO 30892 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:57:00,385 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:00,386 INFO 30892 [-/127.0.0.1/-/27ms POST /citys/setdefault] [egg-sequelize](17ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:57:00,391 INFO 30892 [-/127.0.0.1/-/32ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:57:02,646 INFO 30892 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:57:02,651 INFO 30892 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:02,672 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:02,673 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:57:11,082 INFO 30892 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](6ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:11,083 INFO 30892 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](7ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:57:11,180 INFO 30892 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:57:11,181 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:15,749 INFO 30892 [-/127.0.0.1/-/5ms POST /citys/save] [egg-sequelize](4ms) Executed (default): INSERT INTO `citys` (`id`,`province`,`city`,`district`,`lng`,`lat`,`active`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?,?);
2025-07-31 09:57:15,766 INFO 30892 [-/127.0.0.1/-/5ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:57:15,771 INFO 30892 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](6ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:57:15,772 INFO 30892 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:15,778 INFO 30892 [-/127.0.0.1/-/17ms POST /citys/setdefault] [egg-sequelize](7ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:57:15,785 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:57:15,786 INFO 30892 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:25,667 INFO 30892 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:25,670 INFO 30892 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:57:25,782 INFO 30892 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:57:25,784 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:54,109 INFO 30892 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:57:54,110 INFO 30892 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:54,119 INFO 30892 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:57:54,119 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:58,925 INFO 30892 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:57:58,925 INFO 30892 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:57:59,025 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:57:59,025 INFO 30892 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:58:05,439 INFO 30892 [-/127.0.0.1/-/5ms POST /citys/save] [egg-sequelize](2ms) Executed (default): INSERT INTO `citys` (`id`,`province`,`city`,`district`,`lng`,`lat`,`active`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?,?);
2025-07-31 09:58:05,451 INFO 30892 [-/127.0.0.1/-/5ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:58:05,455 INFO 30892 [-/127.0.0.1/-/9ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:58:05,468 INFO 30892 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:58:05,469 INFO 30892 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:58:05,480 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:58:05,480 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 09:58:21,055 INFO 30892 [-/127.0.0.1/-/10ms POST /citys/setdefault] [egg-sequelize](6ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 09:58:21,060 INFO 30892 [-/127.0.0.1/-/15ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 09:58:21,072 INFO 30892 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 09:58:21,073 INFO 30892 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 09:58:21,084 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 09:58:21,084 INFO 30892 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:26:52,603 INFO 11772 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 10:26:57,186 INFO 11772 [-/127.0.0.1/-/30ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:26:57,189 INFO 11772 [-/127.0.0.1/-/33ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:26:57,203 INFO 11772 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:26:57,205 INFO 11772 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:41:28,654 INFO 9720 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 10:42:22,681 INFO 9720 [-/127.0.0.1/-/48ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:42:22,684 INFO 9720 [-/127.0.0.1/-/51ms POST /citys/list] [egg-sequelize](6ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:42:22,730 INFO 9720 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:42:22,731 INFO 9720 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:44:35,512 INFO 9720 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:44:35,514 INFO 9720 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:44:35,527 INFO 9720 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:44:35,528 INFO 9720 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:44:48,267 INFO 9720 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:44:48,268 INFO 9720 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:44:48,279 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:44:48,279 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:44:55,278 INFO 9720 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:44:55,279 INFO 9720 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:44:55,293 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:44:55,294 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:45:04,257 INFO 9720 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:45:04,258 INFO 9720 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:45:04,273 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:45:04,273 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:45:45,346 INFO 9720 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:45:45,347 INFO 9720 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:45:45,355 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:45:45,356 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:45:56,406 INFO 9720 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:45:56,408 INFO 9720 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:45:56,418 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:45:56,419 INFO 9720 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:46:12,294 INFO 9720 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:46:12,295 INFO 9720 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:46:12,304 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:46:12,304 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:46:23,708 INFO 9720 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:46:23,709 INFO 9720 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:46:23,719 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:46:23,720 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:46:33,207 INFO 9720 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:46:33,208 INFO 9720 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:46:33,217 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:46:33,217 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 10:49:04,686 INFO 9720 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 10:49:04,695 INFO 9720 [-/127.0.0.1/-/22ms POST /citys/list] [egg-sequelize](10ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 10:49:04,715 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 10:49:04,718 INFO 9720 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:01:25,116 INFO 9720 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:01:25,117 INFO 9720 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:01:25,128 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:01:25,129 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:08:54,305 INFO 9720 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:08:54,305 INFO 9720 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:08:54,312 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:08:54,312 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:12:01,962 INFO 9720 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:12:01,962 INFO 9720 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:12:01,970 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:12:01,970 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:12:26,717 INFO 9720 [-/127.0.0.1/-/8ms POST /location/delete] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 1;
2025-07-31 11:12:26,723 INFO 9720 [-/127.0.0.1/-/14ms POST /location/delete] [egg-sequelize](4ms) Executed (default): DELETE FROM `location` WHERE `id` = 1
2025-07-31 11:12:34,106 INFO 9720 [-/127.0.0.1/-/2ms POST /location/save] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 2;
2025-07-31 11:12:34,115 INFO 9720 [-/127.0.0.1/-/11ms POST /location/save] [egg-sequelize](5ms) Executed (default): UPDATE `location` SET `name`=? WHERE `id` = ?
2025-07-31 11:12:38,786 INFO 9720 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:12:38,789 INFO 9720 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:12:38,866 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:12:38,867 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:13:50,875 INFO 9720 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:13:50,876 INFO 9720 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:13:50,976 INFO 9720 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:13:50,977 INFO 9720 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:37:40,079 INFO 22108 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 11:39:12,663 INFO 22108 [-/127.0.0.1/-/72ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:39:12,667 INFO 22108 [-/127.0.0.1/-/76ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:39:12,752 INFO 22108 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:39:12,753 INFO 22108 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:39:34,325 INFO 22108 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:39:34,327 INFO 22108 [-/127.0.0.1/-/16ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:39:34,395 INFO 22108 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:39:34,396 INFO 22108 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:40:34,513 INFO 22108 [-/127.0.0.1/-/31ms POST /location/save] [egg-sequelize](15ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 11:41:52,587 INFO 33752 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 11:42:07,248 INFO 6180 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 11:42:15,457 INFO 6180 [-/127.0.0.1/-/55ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 AND `citys`.`order` IN ('time', 'desc');
2025-07-31 11:42:15,464 ERROR 6180 [-/127.0.0.1/-/58ms POST /citys/list] nodejs.SequelizeDatabaseError: Unknown column 'citys.order' in 'where clause'
    at Query.run (C:\Users\<USER>\Desktop\plan-map\后端\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\plan-map\后端\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.rawSelect (C:\Users\<USER>\Desktop\plan-map\后端\node_modules\sequelize\lib\dialects\abstract\query-interface.js:434:18)
    at async citys.aggregate (C:\Users\<USER>\Desktop\plan-map\后端\node_modules\sequelize\lib\model.js:1277:19)
    at async citys.count (C:\Users\<USER>\Desktop\plan-map\后端\node_modules\sequelize\lib\model.js:1306:20)
    at async Promise.all (index 0)
    at async citys.findAndCountAll (C:\Users\<USER>\Desktop\plan-map\后端\node_modules\sequelize\lib\model.js:1322:27)
    at async Citys.paginate (C:\Users\<USER>\Desktop\plan-map\后端\app\model\citys.js:34:20)
    at async CitysController.list (C:\Users\<USER>\Desktop\plan-map\后端\app\controller\citys.js:74:13)
name: "SequelizeDatabaseError"
parent: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'citys.order' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 AND `citys`.`order` IN ('time', 'desc');"}
original: {"code":"ER_BAD_FIELD_ERROR","errno":1054,"sqlState":"42S22","sqlMessage":"Unknown column 'citys.order' in 'where clause'","sql":"SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 AND `citys`.`order` IN ('time', 'desc');"}
sql: "SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 AND `citys`.`order` IN ('time', 'desc');"
parameters: {}
headers: {"Access-Control-Allow-Origin":"*","vary":"Origin"}
pid: 6180
hostname: DESKTOP-GRM34K4

2025-07-31 11:42:15,471 INFO 6180 [-/127.0.0.1/-/68ms POST /citys/list] [egg-sequelize](12ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 AND `citys`.`order` IN ('time', 'desc') ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:42:15,532 INFO 27788 [egg-sequelize](10ms) Executed (default): SELECT 1+1 AS result
2025-07-31 11:43:32,063 INFO 7412 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 11:43:39,595 INFO 24572 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 11:43:47,931 INFO 10536 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 11:43:48,873 INFO 10536 [-/127.0.0.1/-/51ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:43:48,880 INFO 10536 [-/127.0.0.1/-/57ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:43:48,907 INFO 10536 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:43:48,909 INFO 10536 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:43:57,709 INFO 10536 [-/127.0.0.1/-/4ms POST /location/delete] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 2;
2025-07-31 11:43:57,715 INFO 10536 [-/127.0.0.1/-/9ms POST /location/delete] [egg-sequelize](3ms) Executed (default): DELETE FROM `location` WHERE `id` = 2
2025-07-31 11:46:13,450 INFO 10536 [-/127.0.0.1/-/20ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:46:13,451 INFO 10536 [-/127.0.0.1/-/21ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:46:13,526 INFO 10536 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:46:13,527 INFO 10536 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:51:58,775 INFO 10536 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:51:58,775 INFO 10536 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:51:58,820 INFO 10536 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:51:58,820 INFO 10536 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:52:08,382 INFO 10536 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:52:08,383 INFO 10536 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:52:08,407 INFO 10536 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:52:08,407 INFO 10536 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:52:44,925 INFO 10536 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:52:44,925 INFO 10536 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:52:44,972 INFO 10536 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:52:44,976 INFO 10536 [-/127.0.0.1/-/9ms POST /location/list] [egg-sequelize](6ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:58:58,755 INFO 10536 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:58:58,756 INFO 10536 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:58:58,767 INFO 10536 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:58:58,768 INFO 10536 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:04,370 INFO 10536 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:59:04,371 INFO 10536 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:04,382 INFO 10536 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:59:04,382 INFO 10536 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:17,595 INFO 10536 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:59:17,596 INFO 10536 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:17,615 INFO 10536 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:59:17,616 INFO 10536 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:23,312 INFO 10536 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:59:23,313 INFO 10536 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:23,322 INFO 10536 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:59:23,323 INFO 10536 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:29,460 INFO 10536 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:59:29,461 INFO 10536 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:29,472 INFO 10536 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:59:29,472 INFO 10536 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:42,716 INFO 10536 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 11:59:42,717 INFO 10536 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:42,726 INFO 10536 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 11:59:42,726 INFO 10536 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:48,982 INFO 10536 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-31 11:59:48,982 INFO 10536 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 11:59:48,991 INFO 10536 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-31 11:59:48,991 INFO 10536 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:19:30,241 INFO 10536 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-31 15:19:30,241 INFO 10536 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:19:30,250 INFO 10536 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-31 15:19:30,251 INFO 10536 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:19:40,187 INFO 10536 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-07-31 15:19:40,187 INFO 10536 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:19:40,196 INFO 10536 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-07-31 15:19:40,196 INFO 10536 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:20:05,277 INFO 10536 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:20:05,277 INFO 10536 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:20:05,284 INFO 10536 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:20:05,285 INFO 10536 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:20:53,805 INFO 10536 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:20:53,806 INFO 10536 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:20:53,831 INFO 10536 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:20:53,832 INFO 10536 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:30:10,800 INFO 31552 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 15:30:11,468 INFO 31552 [-/127.0.0.1/-/29ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:30:11,470 INFO 31552 [-/127.0.0.1/-/31ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:30:11,508 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:30:11,509 INFO 31552 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:31:04,981 INFO 31552 [-/127.0.0.1/-/16ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:31:04,982 INFO 31552 [-/127.0.0.1/-/17ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:31:04,994 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:31:04,995 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:31:10,302 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:31:10,302 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:31:10,312 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:31:10,312 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:31:17,312 INFO 31552 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:31:17,313 INFO 31552 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:31:17,322 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:31:17,323 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:31:32,026 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:31:32,026 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:31:32,036 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:31:32,037 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:32:42,023 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:32:42,024 INFO 31552 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:32:42,035 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:32:42,035 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:32:57,832 INFO 31552 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:32:57,833 INFO 31552 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:32:57,843 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:32:57,844 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:36:33,626 INFO 31552 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:36:33,628 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:36:33,638 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:36:33,639 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:38:04,519 INFO 31552 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:38:04,520 INFO 31552 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:38:04,531 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:38:04,531 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:38:15,806 INFO 31552 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:38:15,807 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:38:15,816 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:38:15,817 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:40:48,620 INFO 31552 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:40:48,620 INFO 31552 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:40:48,647 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:40:48,648 INFO 31552 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:41:26,112 INFO 31552 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:41:26,113 INFO 31552 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:41:26,124 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:41:26,124 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:41:34,548 INFO 31552 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:41:34,549 INFO 31552 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:41:34,558 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:41:34,559 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:42:10,950 INFO 31552 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:42:10,951 INFO 31552 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:42:11,056 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:42:11,058 INFO 31552 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:42:35,136 INFO 31552 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:42:35,137 INFO 31552 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:42:35,146 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:42:35,146 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:43:50,418 INFO 31552 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:43:50,419 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:43:50,428 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:43:50,429 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:45:41,899 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:45:41,899 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:45:42,004 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:45:42,004 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:47:06,051 INFO 31552 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:47:06,052 INFO 31552 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:47:06,080 INFO 31552 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:47:06,081 INFO 31552 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:47:44,046 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:47:44,047 INFO 31552 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:47:44,057 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:47:44,057 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:47:51,058 INFO 31552 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:47:51,059 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:47:51,066 INFO 31552 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:47:51,067 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:47:52,715 INFO 31552 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:47:52,716 INFO 31552 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:47:52,725 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:47:52,726 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:47:59,127 INFO 31552 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:47:59,128 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:47:59,228 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:47:59,229 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:48:53,642 INFO 31552 [-/127.0.0.1/-/14ms POST /citys/save] [egg-sequelize](5ms) Executed (default): INSERT INTO `citys` (`id`,`province`,`city`,`district`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 15:48:55,114 INFO 31552 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:48:55,116 INFO 31552 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:48:55,132 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:48:55,133 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:48:58,826 INFO 31552 [-/127.0.0.1/-/2ms POST /location/delete] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 7;
2025-07-31 15:48:58,831 INFO 31552 [-/127.0.0.1/-/7ms POST /location/delete] [egg-sequelize](3ms) Executed (default): DELETE FROM `location` WHERE `id` = 7
2025-07-31 15:49:00,093 INFO 31552 [-/127.0.0.1/-/2ms POST /location/delete] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 5;
2025-07-31 15:49:00,097 INFO 31552 [-/127.0.0.1/-/6ms POST /location/delete] [egg-sequelize](4ms) Executed (default): DELETE FROM `location` WHERE `id` = 5
2025-07-31 15:49:01,110 INFO 31552 [-/127.0.0.1/-/2ms POST /location/delete] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 4;
2025-07-31 15:49:01,114 INFO 31552 [-/127.0.0.1/-/6ms POST /location/delete] [egg-sequelize](4ms) Executed (default): DELETE FROM `location` WHERE `id` = 4
2025-07-31 15:49:02,079 INFO 31552 [-/127.0.0.1/-/3ms POST /location/delete] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 3;
2025-07-31 15:49:02,083 INFO 31552 [-/127.0.0.1/-/7ms POST /location/delete] [egg-sequelize](4ms) Executed (default): DELETE FROM `location` WHERE `id` = 3
2025-07-31 15:49:13,817 INFO 31552 [-/127.0.0.1/-/10ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 15:49:13,822 INFO 31552 [-/127.0.0.1/-/15ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 15:49:13,834 INFO 31552 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:49:13,836 INFO 31552 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:49:13,848 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:49:13,849 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:52:11,889 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:52:11,890 INFO 31552 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:52:11,900 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:52:11,900 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:53:36,160 INFO 31552 [-/127.0.0.1/-/7ms POST /location/save] [egg-sequelize](3ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 15:53:47,281 INFO 31552 [-/127.0.0.1/-/7ms POST /location/save] [egg-sequelize](3ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 15:55:46,160 INFO 31552 [-/127.0.0.1/-/8ms POST /location/save] [egg-sequelize](3ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 15:56:17,142 INFO 31552 [-/127.0.0.1/-/7ms POST /location/save] [egg-sequelize](3ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 15:56:50,568 INFO 31552 [-/127.0.0.1/-/7ms POST /location/save] [egg-sequelize](3ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 15:57:03,935 INFO 31552 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 15:57:03,936 INFO 31552 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 15:57:03,989 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 15:57:03,990 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 15:59:19,579 INFO 31552 [-/127.0.0.1/-/8ms POST /location/save] [egg-sequelize](4ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 16:18:36,923 INFO 31552 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:18:36,925 INFO 31552 [-/127.0.0.1/-/17ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:18:36,952 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:18:36,956 INFO 31552 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:18:53,916 INFO 31552 [-/127.0.0.1/-/9ms POST /location/save] [egg-sequelize](5ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 16:19:59,074 INFO 31552 [-/127.0.0.1/-/5ms POST /location/delete] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 8;
2025-07-31 16:19:59,078 INFO 31552 [-/127.0.0.1/-/9ms POST /location/delete] [egg-sequelize](3ms) Executed (default): DELETE FROM `location` WHERE `id` = 8
2025-07-31 16:22:40,854 INFO 31552 [-/127.0.0.1/-/8ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-07-31 16:22:40,858 INFO 31552 [-/127.0.0.1/-/12ms POST /citys/setdefault] [egg-sequelize](2ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-07-31 16:22:40,872 INFO 31552 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:22:40,873 INFO 31552 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:22:40,901 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:22:40,902 INFO 31552 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:24:34,009 INFO 31552 [-/127.0.0.1/-/54ms POST /location/save] [egg-sequelize](11ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 16:24:57,443 INFO 31552 [-/127.0.0.1/-/7ms POST /location/save] [egg-sequelize](3ms) Executed (default): INSERT INTO `location` (`id`,`name`,`description`,`address`,`lng`,`lat`,`time`,`uid`) VALUES (DEFAULT,?,?,?,?,?,?,?);
2025-07-31 16:25:16,046 INFO 31552 [-/127.0.0.1/-/4ms POST /location/save] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 9;
2025-07-31 16:25:16,051 INFO 31552 [-/127.0.0.1/-/9ms POST /location/save] [egg-sequelize](3ms) Executed (default): UPDATE `location` SET `name`=? WHERE `id` = ?
2025-07-31 16:25:22,923 INFO 31552 [-/127.0.0.1/-/1ms POST /location/save] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`id` = 10;
2025-07-31 16:25:22,937 INFO 31552 [-/127.0.0.1/-/15ms POST /location/save] [egg-sequelize](13ms) Executed (default): UPDATE `location` SET `name`=? WHERE `id` = ?
2025-07-31 16:26:11,269 INFO 31552 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:26:11,270 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:26:11,279 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:26:11,280 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:26:17,595 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:26:17,596 INFO 31552 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:26:17,634 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:26:17,644 INFO 31552 [-/127.0.0.1/-/14ms POST /location/list] [egg-sequelize](11ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:26:19,008 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:26:19,008 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:26:19,038 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:26:19,040 INFO 31552 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:00,388 INFO 31552 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:31:00,389 INFO 31552 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:00,401 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:31:00,401 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:02,887 INFO 31552 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:31:02,887 INFO 31552 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:02,897 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:31:02,898 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:10,314 INFO 31552 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:31:10,315 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:10,322 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:31:10,323 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:19,273 INFO 31552 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:31:19,274 INFO 31552 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:19,282 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:31:19,283 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:31,510 INFO 31552 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:31:31,511 INFO 31552 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:31,519 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:31:31,519 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:33,488 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:31:33,489 INFO 31552 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:31:33,496 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:31:33,497 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:35:30,662 INFO 31552 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:35:30,663 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:35:30,678 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:35:30,679 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:36:12,590 INFO 31552 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:36:12,591 INFO 31552 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:36:12,601 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:36:12,602 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:36:16,744 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:36:16,745 INFO 31552 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:36:16,798 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:36:16,799 INFO 31552 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:37:50,702 INFO 31552 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:37:50,702 INFO 31552 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:37:50,711 INFO 31552 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:37:50,712 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:37:53,938 INFO 31552 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:37:53,939 INFO 31552 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:37:53,948 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 16:37:53,948 INFO 31552 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:38:03,167 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 16:38:03,167 INFO 31552 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 16:38:03,224 INFO 31552 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 16:38:03,228 INFO 31552 [-/127.0.0.1/-/8ms POST /location/list] [egg-sequelize](6ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 17:00:52,461 INFO 35160 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 17:00:52,599 INFO 31180 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-07-31 17:02:12,675 INFO 31180 [-/127.0.0.1/-/47ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 17:02:12,679 INFO 31180 [-/127.0.0.1/-/51ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 17:02:12,808 INFO 31180 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 17:02:12,810 INFO 31180 [-/127.0.0.1/-/9ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 17:02:16,081 INFO 31180 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 17:02:16,082 INFO 31180 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 17:02:16,094 INFO 31180 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 17:02:16,095 INFO 31180 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 17:02:18,655 INFO 31180 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 17:02:18,657 INFO 31180 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 17:02:18,741 INFO 31180 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 17:02:18,742 INFO 31180 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 17:07:08,906 INFO 31180 [-/127.0.0.1/-/16ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 17:07:08,907 INFO 31180 [-/127.0.0.1/-/17ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 17:07:08,932 INFO 31180 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 17:07:08,934 INFO 31180 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 17:07:44,774 INFO 31180 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 17:07:44,775 INFO 31180 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 17:07:44,819 INFO 31180 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 17:07:44,820 INFO 31180 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 17:08:08,681 INFO 31180 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 17:08:08,682 INFO 31180 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 17:08:08,704 INFO 31180 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 17:08:08,704 INFO 31180 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 17:08:16,413 INFO 31180 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 17:08:16,415 INFO 31180 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 17:08:16,497 INFO 31180 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 17:08:16,498 INFO 31180 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 17:09:14,822 INFO 31180 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 17:09:14,822 INFO 31180 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 17:09:14,869 INFO 31180 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 17:09:14,870 INFO 31180 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-07-31 17:09:19,915 INFO 31180 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-07-31 17:09:19,916 INFO 31180 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-07-31 17:09:19,981 INFO 31180 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-07-31 17:09:19,982 INFO 31180 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 08:48:21,316 INFO 10560 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 08:48:32,045 INFO 10560 [-/127.0.0.1/-/45ms POST /citys/list] [egg-sequelize](8ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 08:48:32,048 INFO 10560 [-/127.0.0.1/-/48ms POST /citys/list] [egg-sequelize](12ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 08:48:32,116 INFO 10560 [-/127.0.0.1/-/10ms POST /location/list] [egg-sequelize](6ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 08:48:32,117 INFO 10560 [-/127.0.0.1/-/11ms POST /location/list] [egg-sequelize](8ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 08:49:23,830 INFO 10560 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 08:49:23,831 INFO 10560 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 08:49:23,851 INFO 10560 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 08:49:23,852 INFO 10560 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 08:51:09,451 INFO 10560 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 08:51:09,452 INFO 10560 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 08:51:09,472 INFO 10560 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 08:51:09,473 INFO 10560 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 08:51:49,786 INFO 10560 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 08:51:49,787 INFO 10560 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 08:51:49,814 INFO 10560 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 08:51:49,814 INFO 10560 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 08:51:51,087 INFO 10560 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 08:51:51,088 INFO 10560 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 08:51:51,108 INFO 10560 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 08:51:51,109 INFO 10560 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 08:51:54,496 INFO 10560 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 08:51:54,497 INFO 10560 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 08:51:54,511 INFO 10560 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 08:51:54,512 INFO 10560 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 08:54:11,599 INFO 10560 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 08:54:11,599 INFO 10560 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 08:54:11,615 INFO 10560 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 08:54:11,616 INFO 10560 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 08:54:12,676 INFO 10560 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 08:54:12,677 INFO 10560 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 08:54:12,727 INFO 10560 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 08:54:12,728 INFO 10560 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 08:56:09,973 INFO 25340 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 08:57:04,288 INFO 23640 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-08-01 08:57:13,648 INFO 26836 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 08:57:27,796 INFO 22384 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 08:59:23,967 INFO 25976 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 08:59:28,049 INFO 1896 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 09:05:12,944 INFO 1896 [-/127.0.0.1/-/52ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:05:12,947 INFO 1896 [-/127.0.0.1/-/54ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:05:12,980 INFO 1896 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:05:12,982 INFO 1896 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:26:02,653 INFO 12252 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-08-01 09:31:46,500 INFO 12252 [-/127.0.0.1/-/49ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:31:46,503 INFO 12252 [-/127.0.0.1/-/52ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:31:46,550 INFO 12252 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:31:46,552 INFO 12252 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:35:41,143 INFO 12252 [-/127.0.0.1/-/19ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:35:41,144 INFO 12252 [-/127.0.0.1/-/20ms POST /citys/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:35:41,171 INFO 12252 [-/127.0.0.1/-/8ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:35:41,172 INFO 12252 [-/127.0.0.1/-/9ms POST /location/list] [egg-sequelize](5ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:36:35,730 INFO 12252 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:36:35,731 INFO 12252 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:36:35,770 INFO 12252 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:36:35,771 INFO 12252 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:40:50,941 INFO 12252 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:40:50,942 INFO 12252 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:40:50,972 INFO 12252 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:40:50,973 INFO 12252 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:41:32,063 INFO 12252 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:41:32,064 INFO 12252 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:41:32,133 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:41:32,134 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:46:38,382 INFO 12252 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:46:38,384 INFO 12252 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:46:38,405 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:46:38,405 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:47:28,003 INFO 12252 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:47:28,004 INFO 12252 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:47:28,040 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:47:28,041 INFO 12252 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:48:42,728 INFO 12252 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:48:42,728 INFO 12252 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](4ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:48:42,766 INFO 12252 [-/127.0.0.1/-/8ms POST /location/list] [egg-sequelize](6ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:48:42,767 INFO 12252 [-/127.0.0.1/-/9ms POST /location/list] [egg-sequelize](6ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:48:54,639 INFO 12252 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:48:54,640 INFO 12252 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:48:54,728 INFO 12252 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:48:54,728 INFO 12252 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:49:21,706 INFO 12252 [-/127.0.0.1/-/21ms POST /citys/setdefault] [egg-sequelize](14ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-08-01 09:49:21,711 INFO 12252 [-/127.0.0.1/-/26ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-08-01 09:49:21,725 INFO 12252 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:49:21,726 INFO 12252 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:49:21,737 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:49:21,737 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:50:47,557 INFO 12252 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:50:47,558 INFO 12252 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:50:47,637 INFO 12252 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:50:47,638 INFO 12252 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:50:50,593 INFO 12252 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:50:50,594 INFO 12252 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:50:50,673 INFO 12252 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:50:50,676 INFO 12252 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:51:17,554 INFO 12252 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:51:17,554 INFO 12252 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:51:17,592 INFO 12252 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:51:17,593 INFO 12252 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:51:28,684 INFO 12252 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:51:28,685 INFO 12252 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:51:28,761 INFO 12252 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:51:28,762 INFO 12252 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:51:37,213 INFO 12252 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:51:37,214 INFO 12252 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:51:37,260 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:51:37,261 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:51:56,591 INFO 12252 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:51:56,592 INFO 12252 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:51:56,630 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:51:56,630 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:52:43,330 INFO 12252 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:52:43,331 INFO 12252 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:52:43,384 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:52:43,385 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:52:56,257 INFO 12252 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:52:56,258 INFO 12252 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:52:56,277 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:52:56,278 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:53:06,403 INFO 12252 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:53:06,403 INFO 12252 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:53:06,427 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:53:06,427 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:53:12,277 INFO 12252 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:53:12,278 INFO 12252 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:53:12,309 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:53:12,309 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:53:21,642 INFO 12252 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:53:21,642 INFO 12252 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:53:21,677 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:53:21,677 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:53:28,194 INFO 12252 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:53:28,195 INFO 12252 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:53:28,224 INFO 12252 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:53:28,225 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 09:53:40,351 INFO 12252 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 09:53:40,352 INFO 12252 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 09:53:40,382 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 09:53:40,383 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:01:24,681 INFO 12252 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:01:24,681 INFO 12252 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:01:24,694 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:01:24,695 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:01:27,671 INFO 12252 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:01:27,671 INFO 12252 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:01:27,719 INFO 12252 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:01:27,720 INFO 12252 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:04:51,994 INFO 21432 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 10:08:18,560 INFO 21432 [-/127.0.0.1/-/38ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:08:18,562 INFO 21432 [-/127.0.0.1/-/40ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:08:18,576 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:08:18,578 INFO 21432 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:08:27,599 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:08:27,599 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:08:27,619 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:08:27,620 INFO 21432 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:08:31,535 INFO 21432 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:08:31,535 INFO 21432 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:08:31,623 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:08:31,624 INFO 21432 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:15:34,134 INFO 21432 [-/127.0.0.1/-/9ms POST /user/login] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = '1' LIMIT 1;
2025-08-01 10:16:32,081 INFO 21432 [-/127.0.0.1/-/7ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:16:35,813 INFO 21432 [-/127.0.0.1/-/3ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user1' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:16:54,947 INFO 21432 [-/127.0.0.1/-/13ms POST /user/login] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:17:06,149 INFO 21432 [-/127.0.0.1/-/8ms POST /user/login] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:17:09,640 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:17:49,712 INFO 21432 [-/127.0.0.1/-/14ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:17:49,713 INFO 21432 [-/127.0.0.1/-/15ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:17:49,778 INFO 21432 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:17:49,779 INFO 21432 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:17:53,532 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:17:53,533 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:17:53,542 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:17:53,543 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:18:47,984 INFO 21432 [-/127.0.0.1/-/8ms POST /user/login] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:18:52,716 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:19:31,612 INFO 21432 [-/127.0.0.1/-/7ms POST /user/login] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:19:35,090 INFO 21432 [-/127.0.0.1/-/3ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:19:43,117 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:20:04,558 INFO 21432 [-/127.0.0.1/-/7ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:20:05,602 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:20:05,956 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:20:06,118 INFO 21432 [-/127.0.0.1/-/1ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:20:06,268 INFO 21432 [-/127.0.0.1/-/1ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:20:26,405 INFO 21432 [-/127.0.0.1/-/7ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:20:33,973 INFO 21432 [-/127.0.0.1/-/3ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:20:54,850 INFO 21432 [-/127.0.0.1/-/7ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:21:21,976 INFO 21432 [-/127.0.0.1/-/7ms POST /user/login] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:22:43,253 INFO 21432 [-/127.0.0.1/-/8ms POST /user/login] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:22:47,602 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:22:51,333 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:24:20,452 INFO 21432 [-/127.0.0.1/-/7ms POST /user/login] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:24:47,929 INFO 21432 [-/127.0.0.1/-/11ms POST /user/login] [egg-sequelize](5ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:25:37,497 INFO 21432 [-/127.0.0.1/-/11ms POST /user/login] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:27:03,835 INFO 21432 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:27:27,939 INFO 21432 [-/127.0.0.1/-/6ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:29:38,944 INFO 21432 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:29:44,554 INFO 21432 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:29:44,557 INFO 21432 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:29:44,578 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:29:44,579 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:29:55,313 INFO 21432 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:30:50,314 INFO 21432 [-/127.0.0.1/-/4ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:31:06,732 INFO 21432 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:31:18,627 INFO 21432 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:31:40,177 INFO 21432 [-/127.0.0.1/-/6ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:34:43,885 INFO 21432 [-/127.0.0.1/-/8ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:35:23,793 INFO 21432 [-/127.0.0.1/-/7ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:35:43,493 INFO 21432 [-/127.0.0.1/-/8ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:35:54,214 INFO 21432 [-/127.0.0.1/-/9ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:36:06,586 INFO 21432 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:36:35,063 INFO 21432 [-/127.0.0.1/-/6ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:36:53,423 INFO 21432 [-/127.0.0.1/-/3ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:37:40,108 INFO 21432 [-/127.0.0.1/-/4ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:37:41,551 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:37:58,781 INFO 21432 [-/127.0.0.1/-/4ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:37:59,911 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = 'user' LIMIT 1;
2025-08-01 10:38:51,769 INFO 21432 [-/127.0.0.1/-/4ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:39:22,885 INFO 21432 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:39:43,170 INFO 21432 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:39:43,520 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:39:43,523 INFO 21432 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:39:43,535 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:39:43,535 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:40:20,422 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:40:20,422 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:40:20,431 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:40:20,432 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:41:18,854 INFO 21432 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:41:18,855 INFO 21432 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:41:18,873 INFO 21432 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:41:18,874 INFO 21432 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:41:22,086 INFO 21432 [-/127.0.0.1/-/9ms POST /citys/setdefault] [egg-sequelize](5ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-08-01 10:41:22,090 INFO 21432 [-/127.0.0.1/-/13ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-08-01 10:41:22,102 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:41:22,102 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:41:22,112 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:41:22,113 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:41:37,886 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:41:37,887 INFO 21432 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:41:37,937 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:41:37,938 INFO 21432 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:41:53,012 INFO 21432 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:41:53,013 INFO 21432 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:41:53,025 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:41:53,025 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:43:05,718 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:43:05,718 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:43:05,806 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:43:05,806 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:43:51,937 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-08-01 10:43:51,941 INFO 21432 [-/127.0.0.1/-/11ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-08-01 10:43:51,954 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 10:43:51,955 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:43:51,972 INFO 21432 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 10:43:51,973 INFO 21432 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:49:04,394 INFO 21432 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:49:04,395 INFO 21432 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:49:04,408 INFO 21432 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:49:04,409 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:49:08,274 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:49:08,276 INFO 21432 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:49:08,290 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:49:08,291 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:49:13,068 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:49:13,068 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:49:13,079 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:49:13,080 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:49:38,347 INFO 21432 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:49:38,348 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:49:38,360 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:49:38,361 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:49:40,804 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:49:40,805 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:49:40,817 INFO 21432 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:49:40,818 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:53:31,344 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:53:31,345 INFO 21432 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:53:31,353 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:53:31,354 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:53:58,828 INFO 21432 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:53:58,829 INFO 21432 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:53:58,881 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:53:58,882 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:54:08,470 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan1' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:54:11,174 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:54:20,157 INFO 21432 [-/127.0.0.1/-/1ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:54:32,954 INFO 21432 [-/127.0.0.1/-/4ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:54:46,455 INFO 21432 [-/127.0.0.1/-/4ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:54:53,366 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:54:53,677 INFO 21432 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:54:53,679 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:54:53,698 INFO 21432 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:54:53,699 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:55:52,170 INFO 21432 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:55:52,171 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:55:52,181 INFO 21432 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:55:52,182 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:56:16,623 INFO 21432 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:56:16,624 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:56:16,632 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:56:16,633 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:56:20,029 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:56:20,029 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:56:20,039 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:56:20,039 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:56:29,470 INFO 21432 [-/127.0.0.1/-/2ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:56:29,538 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:56:29,539 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:56:29,567 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:56:29,568 INFO 21432 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:09,793 INFO 21432 [-/127.0.0.1/-/4ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:57:09,858 INFO 21432 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:57:09,860 INFO 21432 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:09,876 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:57:09,877 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:12,471 INFO 21432 [-/127.0.0.1/-/1ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:57:12,537 INFO 21432 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:57:12,538 INFO 21432 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:12,555 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:57:12,556 INFO 21432 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:21,844 INFO 21432 [-/127.0.0.1/-/1ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:57:21,917 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:57:21,918 INFO 21432 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:21,935 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:57:21,935 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:24,070 INFO 21432 [-/127.0.0.1/-/3ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 10:57:24,138 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:57:24,138 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:24,153 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:57:24,154 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:25,277 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:57:25,278 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:25,287 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:57:25,288 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:30,878 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:57:30,879 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:30,889 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:57:30,889 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:32,970 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:57:32,971 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:33,027 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:57:33,027 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:48,150 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:57:48,150 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:48,159 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:57:48,159 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:51,399 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:57:51,399 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:51,408 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:57:51,409 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:59,460 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:57:59,461 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:57:59,471 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:57:59,471 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:09,541 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:58:09,542 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:09,550 INFO 21432 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:58:09,551 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:11,667 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:58:11,668 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:11,677 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:58:11,678 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:17,107 INFO 21432 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:58:17,107 INFO 21432 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:17,116 INFO 21432 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:58:17,117 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:22,281 INFO 21432 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:58:22,282 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:22,292 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:58:22,292 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:24,550 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:58:24,550 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:24,559 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:58:24,559 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:27,375 INFO 21432 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 10:58:27,376 INFO 21432 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 10:58:27,564 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 10:58:27,564 INFO 21432 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:02:43,304 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:02:43,305 INFO 21432 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:02:43,316 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:02:43,316 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:02:57,416 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:02:57,417 INFO 21432 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:02:57,430 INFO 21432 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:02:57,431 INFO 21432 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:03:16,054 INFO 21432 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:03:16,055 INFO 21432 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:03:16,064 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:03:16,064 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:03:19,810 INFO 21432 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:03:19,811 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:03:19,819 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:03:19,819 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:04:35,026 INFO 21432 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:04:35,026 INFO 21432 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:04:35,035 INFO 21432 [-/127.0.0.1/-/1ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:04:35,036 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:05:12,406 INFO 21432 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:05:12,406 INFO 21432 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:05:12,415 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:05:12,416 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:05:17,713 INFO 21432 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:05:17,714 INFO 21432 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:05:17,726 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:05:17,726 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:05:38,155 INFO 21432 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:05:38,155 INFO 21432 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:05:38,182 INFO 21432 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:05:38,183 INFO 21432 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:08:12,343 INFO 21432 [-/127.0.0.1/-/19ms POST /user/register] [egg-sequelize](12ms) Executed (default): INSERT INTO `user` (`id`,`name`,`user`,`pwd`) VALUES (DEFAULT,?,?,?);
2025-08-01 11:09:01,114 INFO 21432 [-/127.0.0.1/-/8ms POST /user/register] [egg-sequelize](3ms) Executed (default): INSERT INTO `user` (`id`,`name`,`user`,`pwd`) VALUES (DEFAULT,?,?,?);
2025-08-01 11:09:21,231 INFO 29548 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 11:09:43,595 INFO 3008 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 11:10:06,120 INFO 33516 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 11:10:16,443 INFO 33516 [-/127.0.0.1/-/30ms POST /user/register] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user`, `pwd`, `time` FROM `user` AS `user` WHERE `user`.`user` = 'user' LIMIT 1;
2025-08-01 11:10:16,454 INFO 33516 [-/127.0.0.1/-/41ms POST /user/register] [egg-sequelize](5ms) Executed (default): INSERT INTO `user` (`id`,`name`,`user`,`pwd`,`time`) VALUES (DEFAULT,?,?,?,?);
2025-08-01 11:10:35,961 INFO 33516 [-/127.0.0.1/-/7ms POST /user/register] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user`, `pwd`, `time` FROM `user` AS `user` WHERE `user`.`user` = 'user' LIMIT 1;
2025-08-01 11:10:50,627 INFO 33376 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 11:10:51,572 INFO 33376 [-/127.0.0.1/-/25ms POST /user/register] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `user`, `pwd`, `time` FROM `user` AS `user` WHERE `user`.`user` = 'user' LIMIT 1;
2025-08-01 11:11:06,575 INFO 33376 [-/127.0.0.1/-/8ms POST /user/register] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user`, `pwd`, `time` FROM `user` AS `user` WHERE `user`.`user` = 'user' LIMIT 1;
2025-08-01 11:11:06,585 INFO 33376 [-/127.0.0.1/-/18ms POST /user/register] [egg-sequelize](4ms) Executed (default): INSERT INTO `user` (`id`,`name`,`user`,`pwd`,`time`) VALUES (DEFAULT,?,?,?,?);
2025-08-01 11:11:11,335 INFO 33376 [-/127.0.0.1/-/2ms POST /user/register] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user`, `pwd`, `time` FROM `user` AS `user` WHERE `user`.`user` = 'user' LIMIT 1;
2025-08-01 11:11:15,296 INFO 33376 [-/127.0.0.1/-/3ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'user' AND `user`.`pwd` = '19951031zhI' LIMIT 1;
2025-08-01 11:11:15,426 INFO 33376 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:11:15,429 INFO 33376 [-/127.0.0.1/-/13ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:11:15,446 INFO 33376 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:11:15,447 INFO 33376 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:11:20,828 INFO 33376 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:11:20,829 INFO 33376 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:11:20,900 INFO 33376 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:11:20,902 INFO 33376 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:12:07,291 INFO 33376 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 7;
2025-08-01 11:12:07,292 INFO 33376 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 7 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:12:07,304 INFO 33376 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 7;
2025-08-01 11:12:07,305 INFO 33376 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 7 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:12:41,661 INFO 33376 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 7;
2025-08-01 11:12:41,661 INFO 33376 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 7 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:12:41,671 INFO 33376 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 7;
2025-08-01 11:12:41,671 INFO 33376 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 7 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:12:44,582 INFO 33376 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 7;
2025-08-01 11:12:44,583 INFO 33376 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 7 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:12:44,614 INFO 33376 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 7;
2025-08-01 11:12:44,620 INFO 33376 [-/127.0.0.1/-/11ms POST /location/list] [egg-sequelize](7ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 7 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:13:04,290 INFO 33376 [-/127.0.0.1/-/5ms POST /user/login] [egg-sequelize](0ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 11:13:04,371 INFO 33376 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 11:13:04,373 INFO 33376 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:13:04,396 INFO 33376 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 11:13:04,397 INFO 33376 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:20:45,387 INFO 33376 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:20:45,389 INFO 33376 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:20:45,413 INFO 33376 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:20:45,415 INFO 33376 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:20:52,062 INFO 33376 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:20:52,063 INFO 33376 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:20:52,088 INFO 33376 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:20:52,091 INFO 33376 [-/127.0.0.1/-/7ms POST /location/list] [egg-sequelize](4ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:21:01,194 INFO 33376 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:21:01,195 INFO 33376 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:21:01,230 INFO 33376 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:21:01,231 INFO 33376 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:21:05,782 INFO 33376 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:21:05,782 INFO 33376 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:21:05,807 INFO 33376 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:21:05,808 INFO 33376 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:21:27,683 INFO 33376 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:21:27,684 INFO 33376 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:21:27,704 INFO 33376 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:21:27,704 INFO 33376 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:22:28,507 INFO 33376 [-/127.0.0.1/-/7ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 11:22:28,640 INFO 33376 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:22:28,642 INFO 33376 [-/127.0.0.1/-/7ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:22:28,667 INFO 33376 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:22:28,668 INFO 33376 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:22:32,993 INFO 33376 [-/127.0.0.1/-/1ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:22:32,994 INFO 33376 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:22:33,069 INFO 33376 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:22:33,070 INFO 33376 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:22:38,767 INFO 33376 [-/127.0.0.1/-/2ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:22:38,768 INFO 33376 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:22:38,873 INFO 33376 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:22:38,874 INFO 33376 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:22:59,623 INFO 33376 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:22:59,624 INFO 33376 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:22:59,646 INFO 33376 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:22:59,647 INFO 33376 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:24:31,105 INFO 33376 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys`;
2025-08-01 11:24:31,106 INFO 33376 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:24:31,145 INFO 33376 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location`;
2025-08-01 11:24:31,146 INFO 33376 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:24:36,426 INFO 33376 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 11:24:36,426 INFO 33376 [-/127.0.0.1/-/3ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:24:36,444 INFO 33376 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 11:24:36,444 INFO 33376 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:24:47,329 INFO 16692 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 11:24:49,565 INFO 16692 [-/127.0.0.1/-/38ms POST /citys/setdefault] [egg-sequelize](14ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-08-01 11:24:49,570 INFO 16692 [-/127.0.0.1/-/42ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-08-01 11:24:49,592 INFO 16692 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 11:24:49,595 INFO 16692 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:24:49,609 INFO 16692 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 11:24:49,610 INFO 16692 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:24:56,483 INFO 16692 [-/127.0.0.1/-/6ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-08-01 11:24:56,488 INFO 16692 [-/127.0.0.1/-/11ms POST /citys/setdefault] [egg-sequelize](3ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-08-01 11:24:56,504 INFO 16692 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 11:24:56,505 INFO 16692 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:24:56,522 INFO 16692 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 11:24:56,523 INFO 16692 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:25:10,948 INFO 16692 [-/127.0.0.1/-/8ms POST /user/login] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `user` FROM `user` AS `user` WHERE `user`.`user` = 'zhangsan' AND `user`.`pwd` = 'zhangsan' LIMIT 1;
2025-08-01 11:25:11,061 INFO 16692 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 11:25:11,063 INFO 16692 [-/127.0.0.1/-/8ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:25:11,088 INFO 16692 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 11:25:11,088 INFO 16692 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:25:33,762 INFO 16692 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 11:25:33,763 INFO 16692 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:25:33,785 INFO 16692 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 11:25:33,786 INFO 16692 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:29:13,628 INFO 26292 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 11:34:47,219 INFO 26292 [-/127.0.0.1/-/51ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 11:34:47,221 INFO 26292 [-/127.0.0.1/-/53ms POST /citys/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:34:47,291 INFO 26292 [-/127.0.0.1/-/6ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 11:34:47,294 INFO 26292 [-/127.0.0.1/-/9ms POST /location/list] [egg-sequelize](3ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:34:48,810 INFO 26292 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 11:34:48,811 INFO 26292 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:34:48,908 INFO 26292 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 11:34:48,909 INFO 26292 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:34:52,080 INFO 26292 [-/127.0.0.1/-/5ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 11:34:52,081 INFO 26292 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:34:52,169 INFO 26292 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 11:34:52,170 INFO 26292 [-/127.0.0.1/-/5ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 11:53:19,101 INFO 26292 [-/127.0.0.1/-/11ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 11:53:19,102 INFO 26292 [-/127.0.0.1/-/12ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 11:53:19,111 INFO 26292 [-/127.0.0.1/-/2ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 11:53:19,112 INFO 26292 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 15:16:19,736 INFO 26292 [-/127.0.0.1/-/9ms POST /citys/list] [egg-sequelize](0ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 15:16:19,737 INFO 26292 [-/127.0.0.1/-/10ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 15:16:19,748 INFO 26292 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 15:16:19,748 INFO 26292 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 15:19:09,914 INFO 26292 [-/127.0.0.1/-/24ms POST /citys/setdefault] [egg-sequelize](15ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `uid` = ?
2025-08-01 15:19:09,919 INFO 26292 [-/127.0.0.1/-/29ms POST /citys/setdefault] [egg-sequelize](4ms) Executed (default): UPDATE `citys` SET `active`=? WHERE `id` = ?
2025-08-01 15:19:09,935 INFO 26292 [-/127.0.0.1/-/4ms POST /citys/list] [egg-sequelize](1ms) Executed (default): SELECT count(*) AS `count` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1;
2025-08-01 15:19:09,937 INFO 26292 [-/127.0.0.1/-/6ms POST /citys/list] [egg-sequelize](2ms) Executed (default): SELECT `id`, `province`, `city`, `district`, `lng`, `lat`, `active`, `time`, `uid` FROM `citys` AS `citys` WHERE `citys`.`uid` = 1 ORDER BY `citys`.`id` DESC LIMIT 0, 10;
2025-08-01 15:19:09,951 INFO 26292 [-/127.0.0.1/-/3ms POST /location/list] [egg-sequelize](1ms) Executed (default): SELECT `id`, `name`, `description`, `address`, `lng`, `lat`, `time`, `uid` FROM `location` AS `location` WHERE `location`.`uid` = 1 ORDER BY `location`.`id` DESC LIMIT 0, 10;
2025-08-01 15:19:09,952 INFO 26292 [-/127.0.0.1/-/4ms POST /location/list] [egg-sequelize](2ms) Executed (default): SELECT count(*) AS `count` FROM `location` AS `location` WHERE `location`.`uid` = 1;
2025-08-01 15:20:43,890 INFO 33188 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 15:26:09,393 INFO 28572 [egg-sequelize](0ms) Executed (default): SELECT 1+1 AS result
2025-08-01 15:27:27,480 INFO 33156 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 15:27:52,542 INFO 4572 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 15:29:06,463 INFO 4780 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 15:29:08,730 INFO 28528 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
2025-08-01 15:29:31,640 INFO 6352 [egg-sequelize](1ms) Executed (default): SELECT 1+1 AS result
