/*
 Navicat Premium Data Transfer

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 50740
 Source Host           : localhost:3306
 Source Schema         : plan-map

 Target Server Type    : MySQL
 Target Server Version : 50740
 File Encoding         : 65001

 Date: 01/08/2025 15:25:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for citys
-- ----------------------------
DROP TABLE IF EXISTS `citys`;
CREATE TABLE `citys`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `province` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '城市',
  `district` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '行政区',
  `lng` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经度',
  `lat` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '纬度',
  `active` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否默认',
  `time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  `uid` int(11) NULL DEFAULT NULL COMMENT '所属用户 关联user',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '中心城市表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of citys
-- ----------------------------
INSERT INTO `citys` VALUES (1, '广西壮族自治区', '南宁市', '青秀区', '108.40482313735464', '22.818248982276994', '0', '2025-07-31 09:58:05', 1);
INSERT INTO `citys` VALUES (2, '贵州省', '贵阳市', '云岩区', '106.71447251732481', '26.604028737423626', '1', '2025-07-31 15:48:53', 1);

-- ----------------------------
-- Table structure for location
-- ----------------------------
DROP TABLE IF EXISTS `location`;
CREATE TABLE `location`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '简介',
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地址',
  `lng` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '经度',
  `lat` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '纬度',
  `time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  `uid` int(11) NULL DEFAULT NULL COMMENT '所属用户 关联user',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '标点表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of location
-- ----------------------------
INSERT INTO `location` VALUES (9, '罗秧河营地（溯溪）', '', '贵州省安顺市关岭布依族苗族自治县', '105.37664408786677', '25.908575249188818', '2025-07-31 15:53:47', 1);
INSERT INTO `location` VALUES (10, '织金奢香洞（洞穴奇观，需徒步）', '', '贵州省毕节市织金县织金桂果旅游区', '105.90110898002804', '26.591265807253077', '2025-07-31 15:55:46', 1);
INSERT INTO `location` VALUES (11, '荔波小七孔（桥梁著名景点）', '', '贵州省黔南布依族苗族自治州荔波县206省道', '107.71430586316846', '25.260091257974064', '2025-07-31 15:56:17', 1);
INSERT INTO `location` VALUES (12, '梵净山景区（氧气山林）', '', '贵州省铜仁市江口县梵净山游客中心旁', '108.78291285001946', '27.847484143899955', '2025-07-31 15:56:50', 1);
INSERT INTO `location` VALUES (13, '关岭冰臼（石头河床景观）', '', '安顺市关岭布依族苗族自治县断桥镇黄果树瀑布下游295公里处', '105.73367004319542', '25.787454997471226', '2025-07-31 15:59:19', 1);
INSERT INTO `location` VALUES (14, '贵阳站', '', '贵州省贵阳市南明区四通街1号', '106.7088306197407', '26.56272241247931', '2025-07-31 16:18:53', 1);
INSERT INTO `location` VALUES (15, '高过河漂流（漂流）', '', '黔东南苗族侗族自治州镇远县羊场镇(高过河漂流景区)高过河风景名胜区东边4公里', '108.31828615396073', '27.260187575961382', '2025-07-31 16:24:33', 1);
INSERT INTO `location` VALUES (16, '洛北河漂流（漂流）', '', '贵州省黔南布依族苗族自治州贵定县G321(沪瑞线)', '107.17353097685123', '26.503635962026426', '2025-07-31 16:24:57', 1);

-- ----------------------------
-- Table structure for paths
-- ----------------------------
DROP TABLE IF EXISTS `paths`;
CREATE TABLE `paths`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路径编号',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路径名称',
  `uid` int(11) NULL DEFAULT NULL COMMENT '所属用户id 关联user',
  `jsons` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '路径详情JSON',
  `time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '路径规划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `user` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账号',
  `pwd` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, '张三', 'zhangsan', 'zhangsan', '2025-07-30 11:30:00');
INSERT INTO `user` VALUES (2, '李四', 'lisi', 'lisi', '2025-07-29 10:15:00');
INSERT INTO `user` VALUES (3, '王五', 'wangwu', 'wangwu', '2025-07-28 09:20:00');
INSERT INTO `user` VALUES (7, 'user', 'user', '19951031zhI', '2025-08-01 11:11:06');

SET FOREIGN_KEY_CHECKS = 1;
